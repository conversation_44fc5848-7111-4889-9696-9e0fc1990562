package com.arina.ai.di;

import android.content.Context;
import android.telecom.TelecomManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SystemModule_ProvideTelecomManagerFactory implements Factory<TelecomManager> {
  private final Provider<Context> contextProvider;

  public SystemModule_ProvideTelecomManagerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public TelecomManager get() {
    return provideTelecomManager(contextProvider.get());
  }

  public static SystemModule_ProvideTelecomManagerFactory create(
      Provider<Context> contextProvider) {
    return new SystemModule_ProvideTelecomManagerFactory(contextProvider);
  }

  public static TelecomManager provideTelecomManager(Context context) {
    return Preconditions.checkNotNullFromProvides(SystemModule.INSTANCE.provideTelecomManager(context));
  }
}
