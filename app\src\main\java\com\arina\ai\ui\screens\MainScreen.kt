package com.arina.ai.ui.screens

import androidx.compose.animation.*
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Mic
import androidx.compose.material.icons.filled.MicOff
import androidx.compose.material.icons.filled.Settings
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.arina.ai.R
import com.arina.ai.service.VoiceInteractionService
import com.arina.ai.ui.components.*
import com.arina.ai.viewmodel.MainViewModel
import com.arina.ai.viewmodel.MainViewEvent

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MainScreen(
    viewModel: MainViewModel,
    voiceInteractionService: VoiceInteractionService,
    modifier: Modifier = Modifier
) {
    val viewState by viewModel.viewState.collectAsState()
    var showSettings by remember { mutableStateOf(false) }

    Scaffold(
        modifier = modifier,
        topBar = {
            CenterAlignedTopAppBar(
                title = { Text("ARINA") },
                actions = {
                    IconButton(onClick = { showSettings = true }) {
                        Icon(Icons.Default.Settings, contentDescription = stringResource(R.string.settings))
                    }
                }
            )
        }
    ) { padding ->
        Box(
            modifier = Modifier
                .fillMaxSize()
                .padding(padding)
        ) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // Emotional Avatar
                PersonalityAvatar(
                    emotionalState = viewState.currentEmotionalState,
                    modifier = Modifier.size(120.dp)
                )

                Spacer(modifier = Modifier.height(24.dp))

                // Voice Waveform
                VoiceWaveformWithState(
                    state = when {
                        viewState.isListening -> RecognitionState.Listening
                        viewState.isProcessing -> RecognitionState.ProcessingInput
                        else -> RecognitionState.Idle
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(80.dp)
                )

                Spacer(modifier = Modifier.height(24.dp))

                // Conversation List
                ConversationList(
                    conversations = viewState.conversations,
                    onBookmark = { conversation ->
                        viewModel.handleEvent(MainViewEvent.BookmarkConversation(conversation))
                    },
                    onDelete = { conversation ->
                        viewModel.handleEvent(MainViewEvent.DeleteConversation(conversation))
                    },
                    modifier = Modifier.weight(1f)
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Microphone Button
                FloatingActionButton(
                    onClick = {
                        if (viewState.isListening) {
                            viewModel.handleEvent(MainViewEvent.StopListening)
                        } else {
                            viewModel.handleEvent(MainViewEvent.StartListening())
                        }
                    },
                    containerColor = MaterialTheme.colorScheme.primary,
                    contentColor = MaterialTheme.colorScheme.onPrimary
                ) {
                    Icon(
                        imageVector = if (viewState.isListening) Icons.Default.MicOff else Icons.Default.Mic,
                        contentDescription = stringResource(
                            if (viewState.isListening) R.string.listening else R.string.tap_to_speak
                        )
                    )
                }
            }

            // Error Snackbar
            viewState.errorMessage?.let { error ->
                Snackbar(
                    modifier = Modifier
                        .padding(16.dp)
                        .align(Alignment.BottomCenter),
                    action = {
                        TextButton(onClick = { viewModel.handleEvent(MainViewEvent.ClearError) }) {
                            Text(stringResource(R.string.retry))
                        }
                    }
                ) {
                    Text(error)
                }
            }

            // Settings Dialog
            if (showSettings) {
                viewState.userPreferences?.let { prefs ->
                    SettingsScreen(
                        preferences = prefs,
                        onSave = { newPrefs ->
                            viewModel.handleEvent(MainViewEvent.UpdatePreferences(newPrefs))
                            showSettings = false
                        },
                        onDismiss = { showSettings = false }
                    )
                }
            }
        }
    }
}