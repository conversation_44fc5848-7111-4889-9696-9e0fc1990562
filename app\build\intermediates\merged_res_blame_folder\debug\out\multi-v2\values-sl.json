{"logs": [{"outputFile": "com.arina.ai.app-mergeDebugResources-64:/values-sl/values-sl.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\895477fa5033432c05b79dc01cd2b72e\\transformed\\appcompat-1.6.1\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2565,2745,2845", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2560,2740,2840,2924"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,217,319,427,514,617,736,817,895,987,1081,1176,1270,1365,1459,1555,1655,1747,1839,1923,2031,2139,2239,2352,2460,2565,2745,9228", "endColumns": "111,101,107,86,102,118,80,77,91,93,94,93,94,93,95,99,91,91,83,107,107,99,112,107,104,179,99,83", "endOffsets": "212,314,422,509,612,731,812,890,982,1076,1171,1265,1360,1454,1550,1650,1742,1834,1918,2026,2134,2234,2347,2455,2560,2740,2840,9307"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d9b68a39eaa82fe8f489e3031923adb9\\transformed\\ui-release\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,281,377,475,560,637,724,816,898,971,1043,1125,1211,1283,1361,1431", "endColumns": "94,80,95,97,84,76,86,91,81,72,71,81,85,71,77,69,120", "endOffsets": "195,276,372,470,555,632,719,811,893,966,1038,1120,1206,1278,1356,1426,1547"}, "to": {"startLines": "40,41,75,76,78,80,81,82,83,84,85,86,87,90,94,95,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4057,4152,8056,8152,8330,8493,8570,8657,8749,8831,8904,8976,9058,9312,9666,9744,9814", "endColumns": "94,80,95,97,84,76,86,91,81,72,71,81,85,71,77,69,120", "endOffsets": "4147,4228,8147,8245,8410,8565,8652,8744,8826,8899,8971,9053,9139,9379,9739,9809,9930"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\afd914afebd159dc02db3b680f36ef47\\transformed\\play-services-basement-16.0.1\\res\\values-sl\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "198", "endOffsets": "445"}, "to": {"startLines": "43", "startColumns": "4", "startOffsets": "4310", "endColumns": "198", "endOffsets": "4504"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\caaa22807b29cbcd058e6ca17cad94f2\\transformed\\material3-1.1.2\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,180,305,419,544,621,713,823,959,1081,1220,1301,1396,1485,1578,1691,1809,1909,2042,2172,2299,2479,2602,2721,2843,2962,3052,3146,3262,3382,3478,3583,3685,3823,3966,4071,4168,4248,4326,4410,4492,4591,4669,4748,4839,4935,5022,5115,5199,5299,5392,5489,5627,5705,5807", "endColumns": "124,124,113,124,76,91,109,135,121,138,80,94,88,92,112,117,99,132,129,126,179,122,118,121,118,89,93,115,119,95,104,101,137,142,104,96,79,77,83,81,98,77,78,90,95,86,92,83,99,92,96,137,77,101,94", "endOffsets": "175,300,414,539,616,708,818,954,1076,1215,1296,1391,1480,1573,1686,1804,1904,2037,2167,2294,2474,2597,2716,2838,2957,3047,3141,3257,3377,3473,3578,3680,3818,3961,4066,4163,4243,4321,4405,4487,4586,4664,4743,4834,4930,5017,5110,5194,5294,5387,5484,5622,5700,5802,5897"}, "to": {"startLines": "29,30,31,32,42,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,77,79,88,91,93,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2845,2970,3095,3209,4233,4509,4601,4711,4847,4969,5108,5189,5284,5373,5466,5579,5697,5797,5930,6060,6187,6367,6490,6609,6731,6850,6940,7034,7150,7270,7366,7471,7573,7711,7854,7959,8250,8415,9144,9384,9567,9935,10013,10092,10183,10279,10366,10459,10543,10643,10736,10833,10971,11049,11151", "endColumns": "124,124,113,124,76,91,109,135,121,138,80,94,88,92,112,117,99,132,129,126,179,122,118,121,118,89,93,115,119,95,104,101,137,142,104,96,79,77,83,81,98,77,78,90,95,86,92,83,99,92,96,137,77,101,94", "endOffsets": "2965,3090,3204,3329,4305,4596,4706,4842,4964,5103,5184,5279,5368,5461,5574,5692,5792,5925,6055,6182,6362,6485,6604,6726,6845,6935,7029,7145,7265,7361,7466,7568,7706,7849,7954,8051,8325,8488,9223,9461,9661,10008,10087,10178,10274,10361,10454,10538,10638,10731,10828,10966,11044,11146,11241"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e44b6092d301126dd6e126ccf393f4dd\\transformed\\core-1.12.0\\res\\values-sl\\values-sl.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,352,456,559,661,778", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "147,249,347,451,554,656,773,874"}, "to": {"startLines": "33,34,35,36,37,38,39,92", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3334,3431,3533,3631,3735,3838,3940,9466", "endColumns": "96,101,97,103,102,101,116,100", "endOffsets": "3426,3528,3626,3730,3833,3935,4052,9562"}}]}]}