package com.arina.ai.ui.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.interaction.collectIsPressedAsState
import androidx.compose.foundation.layout.*
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.unit.dp
import com.arina.ai.ui.theme.ArinaColors
import com.arina.ai.ui.theme.CyberButton

@Composable
fun ArinaButton(
    onClick: () -> Unit,
    modifier: Modifier = Modifier,
    enabled: Boolean = true,
    content: @Composable () -> Unit
) {
    val interactionSource = remember { MutableInteractionSource() }
    val isPressed by interactionSource.collectIsPressedAsState()
    
    val infiniteTransition = rememberInfiniteTransition()
    
    // Glow animation
    val glowAlpha by infiniteTransition.animateFloat(
        initialValue = 0.2f,
        targetValue = 0.6f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        )
    )

    // Border animation
    val borderPhase by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        )
    )

    Box(
        modifier = modifier
            .clip(CyberButton)
            .clickable(
                interactionSource = interactionSource,
                indication = null,
                enabled = enabled,
                onClick = onClick
            )
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(
                        ArinaColors.SurfacePrimary,
                        ArinaColors.SurfaceSecondary
                    )
                )
            )
            .border(
                width = 2.dp,
                brush = Brush.linearGradient(
                    colors = listOf(
                        ArinaColors.NeonCyan,
                        ArinaColors.NeonMagenta,
                        ArinaColors.NeonCyan
                    )
                ),
                shape = CyberButton
            )
            .drawBehind {
                // Draw glowing border
                drawCircle(
                    color = if (isPressed) ArinaColors.NeonMagenta else ArinaColors.NeonCyan,
                    radius = size.maxDimension,
                    alpha = if (isPressed) 0.4f else glowAlpha,
                    style = Stroke(width = 8.dp.toPx())
                )

                // Draw animated corner accents
                val cornerSize = size.width * 0.2f
                repeat(4) { corner ->
                    val rotation = borderPhase + corner * 90f
                    val x = when (corner) {
                        0, 3 -> 0f
                        else -> size.width
                    }
                    val y = when (corner) {
                        0, 1 -> 0f
                        else -> size.height
                    }
                    
                    drawLine(
                        color = ArinaColors.NeonCyan,
                        start = Offset(x, y),
                        end = Offset(
                            x + if (corner % 2 == 0) cornerSize else -cornerSize,
                            y + if (corner < 2) cornerSize else -cornerSize
                        ),
                        strokeWidth = 2.dp.toPx(),
                        alpha = 0.8f
                    )
                }
            }
            .padding(16.dp)
    ) {
        Box(
            modifier = Modifier.fillMaxWidth(),
            contentAlignment = Alignment.Center
        ) {
            val contentColor = if (enabled) {
                if (isPressed) ArinaColors.NeonMagenta else ArinaColors.NeonCyan
            } else {
                ArinaColors.TextDisabled
            }
            
            CompositionLocalProvider(
                LocalContentColor provides contentColor
            ) {
                content()
            }
        }
    }
}

@Composable
fun ArinaButton(
    onClick: () -> Unit,
    text: String,
    modifier: Modifier = Modifier,
    enabled: Boolean = true
) {
    ArinaButton(
        onClick = onClick,
        modifier = modifier,
        enabled = enabled
    ) {
        Text(
            text = text,
            style = MaterialTheme.typography.labelLarge
        )
    }
} 