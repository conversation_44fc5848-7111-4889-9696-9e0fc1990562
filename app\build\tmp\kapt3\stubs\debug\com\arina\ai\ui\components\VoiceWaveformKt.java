package com.arina.ai.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\"\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\b\n\u0002\b\u0007\u001a;\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u00072\b\b\u0002\u0010\b\u001a\u00020\tH\u0007\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\n\u0010\u000b\u001a\u001f\u0010\f\u001a\u00020\u00012\u0006\u0010\r\u001a\u00020\u000e2\b\b\u0002\u0010\u0004\u001a\u00020\u0005H\u0007\u00a2\u0006\u0002\u0010\u000f\u0082\u0002\u000b\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006\u0010"}, d2 = {"VoiceWaveform", "", "amplitude", "", "modifier", "Landroidx/compose/ui/Modifier;", "color", "Landroidx/compose/ui/graphics/Color;", "waveCount", "", "VoiceWaveform-9LQNqLg", "(FLandroidx/compose/ui/Modifier;JI)V", "VoiceWaveformWithState", "state", "error/NonExistentClass", "(Lerror/NonExistentClass;Landroidx/compose/ui/Modifier;)V", "app_debug"})
public final class VoiceWaveformKt {
    
    @androidx.compose.runtime.Composable
    public static final void VoiceWaveformWithState(@org.jetbrains.annotations.NotNull
    error.NonExistentClass state, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
}