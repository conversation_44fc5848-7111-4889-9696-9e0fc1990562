package com.arina.ai.data.database

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.security.crypto.EncryptedSharedPreferences
import androidx.security.crypto.MasterKey
import net.sqlcipher.database.SupportFactory
import com.arina.ai.ArinaApplication

@Database(
    entities = [
        Conversation::class,
        UserPreferences::class
    ],
    version = 1,
    exportSchema = false
)
abstract class ArinaDatabase : RoomDatabase() {
    abstract fun conversationDao(): ConversationDao
    abstract fun userPreferencesDao(): UserPreferencesDao

    companion object {
        @Volatile
        private var INSTANCE: ArinaDatabase? = null

        fun getInstance(context: Context): ArinaDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = createDatabase(context)
                INSTANCE = instance
                instance
            }
        }

        private fun createDatabase(context: Context): ArinaDatabase {
            // Get encryption key from EncryptedSharedPreferences
            val encryptedPrefs = ArinaApplication.encryptedPrefs
            val dbKey = encryptedPrefs.getString("db_key", null) ?: generateAndSaveNewKey(encryptedPrefs)
            
            // Create encrypted database
            val factory = SupportFactory(dbKey.toByteArray())
            
            return Room.databaseBuilder(
                context.applicationContext,
                ArinaDatabase::class.java,
                "arina_database"
            )
            .openHelperFactory(factory)
            .fallbackToDestructiveMigration()
            .build()
        }

        private fun generateAndSaveNewKey(encryptedPrefs: EncryptedSharedPreferences): String {
            val newKey = java.util.UUID.randomUUID().toString()
            encryptedPrefs.edit().putString("db_key", newKey).apply()
            return newKey
        }
    }
} 