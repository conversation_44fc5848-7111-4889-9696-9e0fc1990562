package com.arina.ai.data.database

import android.content.Context
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase

@Database(
    entities = [
        Conversation::class,
        UserPreferences::class
    ],
    version = 1,
    exportSchema = false
)
abstract class ArinaDatabase : RoomDatabase() {
    abstract fun conversationDao(): ConversationDao
    abstract fun userPreferencesDao(): UserPreferencesDao

    companion object {
        @Volatile
        private var INSTANCE: ArinaDatabase? = null

        fun getInstance(context: Context): ArinaDatabase {
            return INSTANCE ?: synchronized(this) {
                val instance = createDatabase(context)
                INSTANCE = instance
                instance
            }
        }

        private fun createDatabase(context: Context): ArinaDatabase {
            return Room.databaseBuilder(
                context.applicationContext,
                ArinaDatabase::class.java,
                "arina_database"
            )
            .fallbackToDestructiveMigration()
            .build()
        }
    }
} 