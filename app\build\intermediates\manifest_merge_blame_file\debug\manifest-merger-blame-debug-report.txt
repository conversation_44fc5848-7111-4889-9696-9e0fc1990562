1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.arina.ai"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="26"
9        android:targetSdkVersion="34" />
10
11    <!-- Basic Permissions -->
12    <uses-permission android:name="android.permission.INTERNET" />
12-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:6:5-67
12-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:6:22-64
13    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
13-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:7:5-79
13-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:7:22-76
14
15    <!-- Audio and Voice Permissions -->
16    <uses-permission android:name="android.permission.RECORD_AUDIO" />
16-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:10:5-71
16-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:10:22-68
17    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
17-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:11:5-80
17-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:11:22-77
18
19    <!-- System Control Permissions -->
20    <uses-permission android:name="android.permission.WRITE_SETTINGS" />
20-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:14:5-15:47
20-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:14:22-70
21    <uses-permission android:name="android.permission.WRITE_SECURE_SETTINGS" />
21-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:16:5-17:47
21-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:16:22-77
22    <uses-permission android:name="android.permission.DEVICE_POWER" />
22-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:18:5-19:47
22-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:18:22-68
23
24    <!-- Phone Control Permissions -->
25    <uses-permission android:name="android.permission.CALL_PHONE" />
25-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:22:5-69
25-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:22:22-66
26    <uses-permission android:name="android.permission.READ_PHONE_STATE" />
26-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:23:5-75
26-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:23:22-72
27    <uses-permission android:name="android.permission.ANSWER_PHONE_CALLS" />
27-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:24:5-77
27-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:24:22-74
28
29    <!-- Notification Permissions -->
30    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
30-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:27:5-77
30-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:27:22-74
31    <uses-permission android:name="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" />
31-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:28:5-29:47
31-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:28:22-90
32
33    <!-- Storage Permissions -->
34    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
34-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:32:5-80
34-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:32:22-77
35    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
35-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:33:5-81
35-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:33:22-78
36
37    <!-- System Alert Window Permission -->
38    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
38-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:36:5-78
38-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:36:22-75
39
40    <!-- Bluetooth Permissions -->
41    <uses-permission android:name="android.permission.BLUETOOTH" />
41-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:39:5-68
41-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:39:22-65
42    <uses-permission android:name="android.permission.BLUETOOTH_ADMIN" />
42-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:40:5-74
42-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:40:22-71
43    <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
43-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:41:5-76
43-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:41:22-73
44
45    <permission
45-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\e44b6092d301126dd6e126ccf393f4dd\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
46        android:name="com.arina.ai.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
46-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\e44b6092d301126dd6e126ccf393f4dd\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
47        android:protectionLevel="signature" />
47-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\e44b6092d301126dd6e126ccf393f4dd\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
48
49    <uses-permission android:name="com.arina.ai.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
49-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\e44b6092d301126dd6e126ccf393f4dd\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
49-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\e44b6092d301126dd6e126ccf393f4dd\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
50
51    <application
51-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:43:5-107:19
52        android:name="com.arina.ai.ArinaApplication"
52-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:44:9-41
53        android:allowBackup="true"
53-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:45:9-35
54        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
54-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\e44b6092d301126dd6e126ccf393f4dd\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
55        android:dataExtractionRules="@xml/data_extraction_rules"
55-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:46:9-65
56        android:debuggable="true"
57        android:extractNativeLibs="false"
58        android:fullBackupContent="@xml/backup_rules"
58-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:47:9-54
59        android:icon="@mipmap/ic_launcher"
59-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:48:9-43
60        android:label="@string/app_name"
60-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:49:9-41
61        android:roundIcon="@mipmap/ic_launcher_round"
61-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:50:9-54
62        android:supportsRtl="true"
62-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:51:9-35
63        android:theme="@style/Theme.Arina" >
63-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:52:9-43
64        <activity
64-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:55:9-65:20
65            android:name="com.arina.ai.MainActivity"
65-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:56:13-41
66            android:exported="true"
66-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:57:13-36
67            android:launchMode="singleTask"
67-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:59:13-44
68            android:theme="@style/Theme.Arina"
68-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:58:13-47
69            android:windowSoftInputMode="adjustResize" >
69-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:60:13-55
70            <intent-filter>
70-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:61:13-64:29
71                <action android:name="android.intent.action.MAIN" />
71-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:62:17-69
71-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:62:25-66
72
73                <category android:name="android.intent.category.LAUNCHER" />
73-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:63:17-77
73-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:63:27-74
74            </intent-filter>
75        </activity>
76        <activity
76-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:67:9-80:20
77            android:name="com.arina.ai.debug.DebugActivity"
77-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:68:13-48
78            android:exported="true"
78-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:69:13-36
79            android:label="ARINA Debug"
79-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:71:13-40
80            android:theme="@style/Theme.Arina" >
80-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:70:13-47
81            <intent-filter>
81-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:72:13-79:29
82                <action android:name="android.intent.action.VIEW" />
82-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:73:17-69
82-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:73:25-66
83
84                <category android:name="android.intent.category.DEFAULT" />
84-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:74:17-76
84-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:74:27-73
85                <category android:name="android.intent.category.BROWSABLE" />
85-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:75:17-78
85-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:75:27-75
86
87                <data
87-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:76:17-78:46
88                    android:host="debug"
88-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:77:21-41
89                    android:scheme="arina" />
89-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:78:21-43
90            </intent-filter>
91        </activity>
92
93        <!-- Device Admin Receiver -->
94        <receiver
94-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:83:9-94:20
95            android:name="com.arina.ai.receiver.ArinaDeviceAdminReceiver"
95-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:84:13-62
96            android:description="@string/device_admin_description"
96-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:85:13-67
97            android:exported="true"
97-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:86:13-36
98            android:permission="android.permission.BIND_DEVICE_ADMIN" >
98-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:87:13-70
99            <meta-data
99-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:88:13-90:56
100                android:name="android.app.device_admin"
100-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:89:17-56
101                android:resource="@xml/device_admin" />
101-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:90:17-53
102
103            <intent-filter>
103-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:91:13-93:29
104                <action android:name="android.app.action.DEVICE_ADMIN_ENABLED" />
104-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:92:17-82
104-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:92:25-79
105            </intent-filter>
106        </receiver>
107
108        <!-- Notification Listener Service -->
109        <service
109-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:97:9-105:19
110            android:name="com.arina.ai.service.ArinaNotificationService"
110-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:98:13-61
111            android:exported="false"
111-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:101:13-37
112            android:label="@string/app_name"
112-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:99:13-45
113            android:permission="android.permission.BIND_NOTIFICATION_LISTENER_SERVICE" >
113-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:100:13-87
114            <intent-filter>
114-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:102:13-104:29
115                <action android:name="android.service.notification.NotificationListenerService" />
115-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:103:17-99
115-->C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:103:25-96
116            </intent-filter>
117        </service>
118
119        <activity
119-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0efd23bbd0106786d0ccaeef17d4176e\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
120            android:name="androidx.compose.ui.tooling.PreviewActivity"
120-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0efd23bbd0106786d0ccaeef17d4176e\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
121            android:exported="true" />
121-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0efd23bbd0106786d0ccaeef17d4176e\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
122        <activity
122-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2705adae8bd7b86a2eeefb93e6d40613\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
123            android:name="androidx.activity.ComponentActivity"
123-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2705adae8bd7b86a2eeefb93e6d40613\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
124            android:exported="true" />
124-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2705adae8bd7b86a2eeefb93e6d40613\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
125
126        <provider
126-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\0ba9bdec507852338c756d6babae2709\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
127            android:name="androidx.startup.InitializationProvider"
127-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\0ba9bdec507852338c756d6babae2709\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
128            android:authorities="com.arina.ai.androidx-startup"
128-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\0ba9bdec507852338c756d6babae2709\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
129            android:exported="false" >
129-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\0ba9bdec507852338c756d6babae2709\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
130            <meta-data
130-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\0ba9bdec507852338c756d6babae2709\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
131                android:name="androidx.emoji2.text.EmojiCompatInitializer"
131-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\0ba9bdec507852338c756d6babae2709\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
132                android:value="androidx.startup" />
132-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\0ba9bdec507852338c756d6babae2709\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
133            <meta-data
133-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b50e821ea9d6b95ff5b074abff6c26d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
134                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
134-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b50e821ea9d6b95ff5b074abff6c26d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
135                android:value="androidx.startup" />
135-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b50e821ea9d6b95ff5b074abff6c26d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
136            <meta-data
136-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
137                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
137-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
138                android:value="androidx.startup" />
138-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
139        </provider>
140
141        <service
141-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f445b7eee75118b9dc957b2fbd2da47\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
142            android:name="androidx.room.MultiInstanceInvalidationService"
142-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f445b7eee75118b9dc957b2fbd2da47\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
143            android:directBootAware="true"
143-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f445b7eee75118b9dc957b2fbd2da47\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
144            android:exported="false" />
144-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f445b7eee75118b9dc957b2fbd2da47\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
145
146        <receiver
146-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
147            android:name="androidx.profileinstaller.ProfileInstallReceiver"
147-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
148            android:directBootAware="false"
148-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
149            android:enabled="true"
149-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
150            android:exported="true"
150-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
151            android:permission="android.permission.DUMP" >
151-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
152            <intent-filter>
152-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
153                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
153-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
153-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
154            </intent-filter>
155            <intent-filter>
155-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
156                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
156-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
156-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
157            </intent-filter>
158            <intent-filter>
158-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
159                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
159-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
159-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
160            </intent-filter>
161            <intent-filter>
161-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
162                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
162-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
162-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
163            </intent-filter>
164        </receiver>
165
166        <meta-data
166-->[com.google.android.gms:play-services-basement:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\afd914afebd159dc02db3b680f36ef47\transformed\play-services-basement-16.0.1\AndroidManifest.xml:23:9-25:69
167            android:name="com.google.android.gms.version"
167-->[com.google.android.gms:play-services-basement:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\afd914afebd159dc02db3b680f36ef47\transformed\play-services-basement-16.0.1\AndroidManifest.xml:24:13-58
168            android:value="@integer/google_play_services_version" />
168-->[com.google.android.gms:play-services-basement:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\afd914afebd159dc02db3b680f36ef47\transformed\play-services-basement-16.0.1\AndroidManifest.xml:25:13-66
169    </application>
170
171</manifest>
