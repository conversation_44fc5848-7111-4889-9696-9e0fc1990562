package com.arina.ai.data.database;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class UserPreferencesDao_Impl implements UserPreferencesDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<UserPreferences> __insertionAdapterOfUserPreferences;

  private final EntityDeletionOrUpdateAdapter<UserPreferences> __updateAdapterOfUserPreferences;

  public UserPreferencesDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfUserPreferences = new EntityInsertionAdapter<UserPreferences>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR REPLACE INTO `user_preferences` (`id`,`selectedModel`,`wakeWordEnabled`,`notificationSpeakerEnabled`,`emotionalResponsesEnabled`,`voiceSpeed`,`voicePitch`,`theme`) VALUES (?,?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final UserPreferences entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getSelectedModel() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getSelectedModel());
        }
        final int _tmp = entity.getWakeWordEnabled() ? 1 : 0;
        statement.bindLong(3, _tmp);
        final int _tmp_1 = entity.getNotificationSpeakerEnabled() ? 1 : 0;
        statement.bindLong(4, _tmp_1);
        final int _tmp_2 = entity.getEmotionalResponsesEnabled() ? 1 : 0;
        statement.bindLong(5, _tmp_2);
        statement.bindDouble(6, entity.getVoiceSpeed());
        statement.bindDouble(7, entity.getVoicePitch());
        if (entity.getTheme() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getTheme());
        }
      }
    };
    this.__updateAdapterOfUserPreferences = new EntityDeletionOrUpdateAdapter<UserPreferences>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `user_preferences` SET `id` = ?,`selectedModel` = ?,`wakeWordEnabled` = ?,`notificationSpeakerEnabled` = ?,`emotionalResponsesEnabled` = ?,`voiceSpeed` = ?,`voicePitch` = ?,`theme` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final UserPreferences entity) {
        statement.bindLong(1, entity.getId());
        if (entity.getSelectedModel() == null) {
          statement.bindNull(2);
        } else {
          statement.bindString(2, entity.getSelectedModel());
        }
        final int _tmp = entity.getWakeWordEnabled() ? 1 : 0;
        statement.bindLong(3, _tmp);
        final int _tmp_1 = entity.getNotificationSpeakerEnabled() ? 1 : 0;
        statement.bindLong(4, _tmp_1);
        final int _tmp_2 = entity.getEmotionalResponsesEnabled() ? 1 : 0;
        statement.bindLong(5, _tmp_2);
        statement.bindDouble(6, entity.getVoiceSpeed());
        statement.bindDouble(7, entity.getVoicePitch());
        if (entity.getTheme() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getTheme());
        }
        statement.bindLong(9, entity.getId());
      }
    };
  }

  @Override
  public Object insertUserPreferences(final UserPreferences preferences,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfUserPreferences.insert(preferences);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateUserPreferences(final UserPreferences preferences,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfUserPreferences.handle(preferences);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object getUserPreferences(final Continuation<? super UserPreferences> $completion) {
    final String _sql = "SELECT * FROM user_preferences WHERE id = 1";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<UserPreferences>() {
      @Override
      @Nullable
      public UserPreferences call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfSelectedModel = CursorUtil.getColumnIndexOrThrow(_cursor, "selectedModel");
          final int _cursorIndexOfWakeWordEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "wakeWordEnabled");
          final int _cursorIndexOfNotificationSpeakerEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "notificationSpeakerEnabled");
          final int _cursorIndexOfEmotionalResponsesEnabled = CursorUtil.getColumnIndexOrThrow(_cursor, "emotionalResponsesEnabled");
          final int _cursorIndexOfVoiceSpeed = CursorUtil.getColumnIndexOrThrow(_cursor, "voiceSpeed");
          final int _cursorIndexOfVoicePitch = CursorUtil.getColumnIndexOrThrow(_cursor, "voicePitch");
          final int _cursorIndexOfTheme = CursorUtil.getColumnIndexOrThrow(_cursor, "theme");
          final UserPreferences _result;
          if (_cursor.moveToFirst()) {
            final int _tmpId;
            _tmpId = _cursor.getInt(_cursorIndexOfId);
            final String _tmpSelectedModel;
            if (_cursor.isNull(_cursorIndexOfSelectedModel)) {
              _tmpSelectedModel = null;
            } else {
              _tmpSelectedModel = _cursor.getString(_cursorIndexOfSelectedModel);
            }
            final boolean _tmpWakeWordEnabled;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfWakeWordEnabled);
            _tmpWakeWordEnabled = _tmp != 0;
            final boolean _tmpNotificationSpeakerEnabled;
            final int _tmp_1;
            _tmp_1 = _cursor.getInt(_cursorIndexOfNotificationSpeakerEnabled);
            _tmpNotificationSpeakerEnabled = _tmp_1 != 0;
            final boolean _tmpEmotionalResponsesEnabled;
            final int _tmp_2;
            _tmp_2 = _cursor.getInt(_cursorIndexOfEmotionalResponsesEnabled);
            _tmpEmotionalResponsesEnabled = _tmp_2 != 0;
            final float _tmpVoiceSpeed;
            _tmpVoiceSpeed = _cursor.getFloat(_cursorIndexOfVoiceSpeed);
            final float _tmpVoicePitch;
            _tmpVoicePitch = _cursor.getFloat(_cursorIndexOfVoicePitch);
            final String _tmpTheme;
            if (_cursor.isNull(_cursorIndexOfTheme)) {
              _tmpTheme = null;
            } else {
              _tmpTheme = _cursor.getString(_cursorIndexOfTheme);
            }
            _result = new UserPreferences(_tmpId,_tmpSelectedModel,_tmpWakeWordEnabled,_tmpNotificationSpeakerEnabled,_tmpEmotionalResponsesEnabled,_tmpVoiceSpeed,_tmpVoicePitch,_tmpTheme);
          } else {
            _result = null;
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
