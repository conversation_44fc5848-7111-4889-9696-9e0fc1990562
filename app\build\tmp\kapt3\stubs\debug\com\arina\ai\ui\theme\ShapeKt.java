package com.arina.ai.ui.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\"\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0007\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\u0003\"\u0011\u0010\u0000\u001a\u00020\u0001\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0002\u0010\u0003\"\u0011\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0006\u0010\u0007\"\u0011\u0010\b\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\u0007\"\u0011\u0010\n\u001a\u00020\u0005\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000b\u0010\u0007\"\u0011\u0010\f\u001a\u00020\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000f\"\u0011\u0010\u0010\u001a\u00020\u0011\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0013\u00a8\u0006\u0014"}, d2 = {"ArinaShapes", "Landroidx/compose/material3/Shapes;", "getArinaShapes", "()Landroidx/compose/material3/Shapes;", "CyberButton", "Lcom/arina/ai/ui/theme/CyberShape;", "getCyberButton", "()Lcom/arina/ai/ui/theme/CyberShape;", "CyberCard", "getCyberCard", "CyberDialog", "getCyberDialog", "DiagonalCard", "Lcom/arina/ai/ui/theme/DiagonalCutShape;", "getDiagonalCard", "()Lcom/arina/ai/ui/theme/DiagonalCutShape;", "HexagonAvatar", "Lcom/arina/ai/ui/theme/HexagonShape;", "getHexagonAvatar", "()Lcom/arina/ai/ui/theme/HexagonShape;", "app_debug"})
public final class ShapeKt {
    @org.jetbrains.annotations.NotNull
    private static final androidx.compose.material3.Shapes ArinaShapes = null;
    @org.jetbrains.annotations.NotNull
    private static final com.arina.ai.ui.theme.CyberShape CyberButton = null;
    @org.jetbrains.annotations.NotNull
    private static final com.arina.ai.ui.theme.CyberShape CyberCard = null;
    @org.jetbrains.annotations.NotNull
    private static final com.arina.ai.ui.theme.CyberShape CyberDialog = null;
    @org.jetbrains.annotations.NotNull
    private static final com.arina.ai.ui.theme.HexagonShape HexagonAvatar = null;
    @org.jetbrains.annotations.NotNull
    private static final com.arina.ai.ui.theme.DiagonalCutShape DiagonalCard = null;
    
    @org.jetbrains.annotations.NotNull
    public static final androidx.compose.material3.Shapes getArinaShapes() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public static final com.arina.ai.ui.theme.CyberShape getCyberButton() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public static final com.arina.ai.ui.theme.CyberShape getCyberCard() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public static final com.arina.ai.ui.theme.CyberShape getCyberDialog() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public static final com.arina.ai.ui.theme.HexagonShape getHexagonAvatar() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public static final com.arina.ai.ui.theme.DiagonalCutShape getDiagonalCard() {
        return null;
    }
}