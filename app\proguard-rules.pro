# Arina AI ProGuard Configuration
# Security-focused obfuscation and optimization rules

# Keep application class
-keep class com.arina.ai.ArinaApplication { *; }

# Keep MainActivity for proper functioning
-keep class com.arina.ai.MainActivity { *; }

# Security: Obfuscate sensitive classes but keep necessary functionality
-keep class com.arina.ai.utils.SecureStorage {
    public <methods>;
}

-keep class com.arina.ai.utils.SecurityManager {
    public <methods>;
}

# Keep API service interfaces for Retrofit
-keep interface com.arina.ai.data.api.A4FApiService { *; }
-keep class com.arina.ai.data.api.** { *; }

# Keep data classes for JSON serialization
-keep class com.arina.ai.data.api.ChatCompletionRequest { *; }
-keep class com.arina.ai.data.api.ChatCompletionResponse { *; }
-keep class com.arina.ai.data.api.ChatMessage { *; }
-keep class com.arina.ai.data.api.ChatChoice { *; }
-keep class com.arina.ai.data.api.Usage { *; }
-keep class com.arina.ai.data.api.ModelsResponse { *; }
-keep class com.arina.ai.data.api.ModelInfo { *; }

# Keep ViewModels and UI State classes
-keep class com.arina.ai.ui.screens.**.* { *; }
-keep class com.arina.ai.viewmodel.** { *; }

# Retrofit and OkHttp
-dontwarn retrofit2.**
-keep class retrofit2.** { *; }
-keepattributes Signature
-keepattributes Exceptions
-keepclasseswithmembers class * {
    @retrofit2.http.* <methods>;
}

# OkHttp
-dontwarn okhttp3.**
-dontwarn okio.**
-dontwarn javax.annotation.**
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase

# Gson
-keepattributes Signature
-keepattributes *Annotation*
-dontwarn sun.misc.**
-keep class com.google.gson.** { *; }
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# Jetpack Compose
-keep class androidx.compose.** { *; }
-keep class kotlin.Metadata { *; }
-dontwarn androidx.compose.**

# Coroutines
-keepnames class kotlinx.coroutines.internal.MainDispatcherFactory {}
-keepnames class kotlinx.coroutines.CoroutineExceptionHandler {}
-keepclassmembernames class kotlinx.** {
    volatile <fields>;
}

# EncryptedSharedPreferences
-keep class androidx.security.crypto.** { *; }
-dontwarn androidx.security.crypto.**

# Picovoice (when added)
-keep class ai.picovoice.** { *; }
-dontwarn ai.picovoice.**

# Lottie (when added)
-keep class com.airbnb.lottie.** { *; }
-dontwarn com.airbnb.lottie.**

# Security: Remove logging in release builds
-assumenosideeffects class android.util.Log {
    public static boolean isLoggable(java.lang.String, int);
    public static int v(...);
    public static int i(...);
    public static int w(...);
    public static int d(...);
    public static int e(...);
}

# Security: Obfuscate package names but keep essential structure
-repackageclasses 'a'
-allowaccessmodification

# Security: Remove debug information
-renamesourcefileattribute SourceFile

# Optimization settings
-optimizations !code/simplification/arithmetic,!code/simplification/cast,!field/*,!class/merging/*
-optimizationpasses 5

# Keep native methods
-keepclasseswithmembernames class * {
    native <methods>;
}

# Keep custom exceptions
-keep public class * extends java.lang.Exception

# Security: Don't warn about missing classes that are not used
-dontwarn java.lang.invoke.**
-dontwarn **$$serializer
-dontwarn javax.**
-dontwarn org.conscrypt.**
-dontwarn org.bouncycastle.**
-dontwarn org.openjsse.**
