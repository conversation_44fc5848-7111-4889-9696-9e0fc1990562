package com.arina.ai.ui.theme

import android.app.Activity
import android.os.Build
import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.SideEffect
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalView
import androidx.core.view.WindowCompat

private val DarkColorScheme = darkColorScheme(
    primary = ArinaColors.NeonCyan,
    onPrimary = Color.Black,
    primaryContainer = ArinaColors.CyanGlow,
    onPrimaryContainer = ArinaColors.NeonCyan,
    
    secondary = ArinaColors.NeonMagenta,
    onSecondary = Color.Black,
    secondaryContainer = ArinaColors.MagentaGlow,
    onSecondaryContainer = ArinaColors.NeonMagenta,
    
    tertiary = ArinaColors.NeonPurple,
    onTertiary = Color.Black,
    tertiaryContainer = ArinaColors.VioletGlow,
    onTertiaryContainer = ArinaColors.NeonPurple,
    
    background = ArinaColors.DeepBlack,
    onBackground = ArinaColors.TextPrimary,
    
    surface = ArinaColors.SurfacePrimary,
    onSurface = ArinaColors.TextPrimary,
    surfaceVariant = ArinaColors.SurfaceSecondary,
    onSurfaceVariant = ArinaColors.TextSecondary,
    
    error = ArinaColors.Error,
    onError = Color.Black,
    errorContainer = ArinaColors.Error.copy(alpha = 0.2f),
    onErrorContainer = ArinaColors.Error,
    
    outline = ArinaColors.BorderPrimary,
    outlineVariant = ArinaColors.BorderSecondary
)

private val LightColorScheme = darkColorScheme(
    // We use dark theme colors for light theme as well to maintain the cyberpunk aesthetic
    primary = ArinaColors.NeonCyan,
    onPrimary = Color.Black,
    primaryContainer = ArinaColors.CyanGlow,
    onPrimaryContainer = ArinaColors.NeonCyan,
    
    secondary = ArinaColors.NeonMagenta,
    onSecondary = Color.Black,
    secondaryContainer = ArinaColors.MagentaGlow,
    onSecondaryContainer = ArinaColors.NeonMagenta,
    
    tertiary = ArinaColors.NeonPurple,
    onTertiary = Color.Black,
    tertiaryContainer = ArinaColors.VioletGlow,
    onTertiaryContainer = ArinaColors.NeonPurple,
    
    background = ArinaColors.DeepBlack,
    onBackground = ArinaColors.TextPrimary,
    
    surface = ArinaColors.SurfacePrimary,
    onSurface = ArinaColors.TextPrimary,
    surfaceVariant = ArinaColors.SurfaceSecondary,
    onSurfaceVariant = ArinaColors.TextSecondary,
    
    error = ArinaColors.Error,
    onError = Color.Black,
    errorContainer = ArinaColors.Error.copy(alpha = 0.2f),
    onErrorContainer = ArinaColors.Error,
    
    outline = ArinaColors.BorderPrimary,
    outlineVariant = ArinaColors.BorderSecondary
)

@Composable
fun ArinaTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    dynamicColor: Boolean = false,
    content: @Composable () -> Unit
) {
    val colorScheme = when {
        dynamicColor && Build.VERSION.SDK_INT >= Build.VERSION_CODES.S -> {
            val context = LocalContext.current
            if (darkTheme) dynamicDarkColorScheme(context) else dynamicLightColorScheme(context)
        }
        darkTheme -> DarkColorScheme
        else -> LightColorScheme
    }

    val view = LocalView.current
    if (!view.isInEditMode) {
        SideEffect {
            val window = (view.context as Activity).window
            window.statusBarColor = colorScheme.background.toArgb()
            WindowCompat.getInsetsController(window, view).isAppearanceLightStatusBars = !darkTheme
        }
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = ArinaTypography,
        content = content
    )
}

object ArinaThemeUtils {
    fun createNeonGlow(color: Color, alpha: Float = 0.4f): Color {
        return color.copy(alpha = alpha)
    }
} 