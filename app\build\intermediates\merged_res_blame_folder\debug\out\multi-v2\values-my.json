{"logs": [{"outputFile": "com.arina.ai.app-mergeDebugResources-64:/values-my/values-my.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\caaa22807b29cbcd058e6ca17cad94f2\\transformed\\material3-1.1.2\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,188,318,425,555,635,731,845,990,1110,1269,1351,1446,1535,1633,1749,1872,1972,2094,2221,2361,2525,2643,2756,2872,2996,3086,3179,3306,3439,3537,3645,3746,3867,3992,4091,4189,4266,4344,4430,4512,4624,4700,4780,4876,4974,5066,5160,5243,5344,5439,5535,5652,5728,5847", "endColumns": "132,129,106,129,79,95,113,144,119,158,81,94,88,97,115,122,99,121,126,139,163,117,112,115,123,89,92,126,132,97,107,100,120,124,98,97,76,77,85,81,111,75,79,95,97,91,93,82,100,94,95,116,75,118,113", "endOffsets": "183,313,420,550,630,726,840,985,1105,1264,1346,1441,1530,1628,1744,1867,1967,2089,2216,2356,2520,2638,2751,2867,2991,3081,3174,3301,3434,3532,3640,3741,3862,3987,4086,4184,4261,4339,4425,4507,4619,4695,4775,4871,4969,5061,5155,5238,5339,5434,5530,5647,5723,5842,5956"}, "to": {"startLines": "29,30,31,32,42,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,77,79,88,91,93,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2866,2999,3129,3236,4290,4583,4679,4793,4938,5058,5217,5299,5394,5483,5581,5697,5820,5920,6042,6169,6309,6473,6591,6704,6820,6944,7034,7127,7254,7387,7485,7593,7694,7815,7940,8039,8345,8505,9243,9490,9673,10058,10134,10214,10310,10408,10500,10594,10677,10778,10873,10969,11086,11162,11281", "endColumns": "132,129,106,129,79,95,113,144,119,158,81,94,88,97,115,122,99,121,126,139,163,117,112,115,123,89,92,126,132,97,107,100,120,124,98,97,76,77,85,81,111,75,79,95,97,91,93,82,100,94,95,116,75,118,113", "endOffsets": "2994,3124,3231,3361,4365,4674,4788,4933,5053,5212,5294,5389,5478,5576,5692,5815,5915,6037,6164,6304,6468,6586,6699,6815,6939,7029,7122,7249,7382,7480,7588,7689,7810,7935,8034,8132,8417,8578,9324,9567,9780,10129,10209,10305,10403,10495,10589,10672,10773,10868,10964,11081,11157,11276,11390"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\895477fa5033432c05b79dc01cd2b72e\\transformed\\appcompat-1.6.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,839,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1854,1939,2054,2163,2262,2388,2495,2603,2763,2866", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,834,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1849,1934,2049,2158,2257,2383,2490,2598,2758,2861,2947"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,839,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1854,1939,2054,2163,2262,2388,2495,2603,2763,9329", "endColumns": "112,106,115,86,108,122,78,77,90,92,94,93,99,92,94,93,90,90,84,114,108,98,125,106,107,159,102,85", "endOffsets": "213,320,436,523,632,755,834,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1849,1934,2049,2158,2257,2383,2490,2598,2758,2861,9410"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\afd914afebd159dc02db3b680f36ef47\\transformed\\play-services-basement-16.0.1\\res\\values-my\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "212", "endOffsets": "459"}, "to": {"startLines": "43", "startColumns": "4", "startOffsets": "4370", "endColumns": "212", "endOffsets": "4578"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d9b68a39eaa82fe8f489e3031923adb9\\transformed\\ui-release\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,199,287,391,495,578,662,761,850,932,998,1065,1152,1238,1313,1394,1460", "endColumns": "93,87,103,103,82,83,98,88,81,65,66,86,85,74,80,65,125", "endOffsets": "194,282,386,490,573,657,756,845,927,993,1060,1147,1233,1308,1389,1455,1581"}, "to": {"startLines": "40,41,75,76,78,80,81,82,83,84,85,86,87,90,94,95,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4108,4202,8137,8241,8422,8583,8667,8766,8855,8937,9003,9070,9157,9415,9785,9866,9932", "endColumns": "93,87,103,103,82,83,98,88,81,65,66,86,85,74,80,65,125", "endOffsets": "4197,4285,8236,8340,8500,8662,8761,8850,8932,8998,9065,9152,9238,9485,9861,9927,10053"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e44b6092d301126dd6e126ccf393f4dd\\transformed\\core-1.12.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "33,34,35,36,37,38,39,92", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3366,3469,3573,3676,3778,3883,3989,9572", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "3464,3568,3671,3773,3878,3984,4103,9668"}}]}]}