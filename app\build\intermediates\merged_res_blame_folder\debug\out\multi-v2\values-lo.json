{"logs": [{"outputFile": "com.arina.ai.app-mergeDebugResources-64:/values-lo/values-lo.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d9b68a39eaa82fe8f489e3031923adb9\\transformed\\ui-release\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,272,381,479,568,657,747,833,916,981,1047,1127,1211,1285,1363,1429", "endColumns": "89,76,108,97,88,88,89,85,82,64,65,79,83,73,77,65,120", "endOffsets": "190,267,376,474,563,652,742,828,911,976,1042,1122,1206,1280,1358,1424,1545"}, "to": {"startLines": "40,41,75,76,78,80,81,82,83,84,85,86,87,90,94,95,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3909,3999,7722,7831,8005,8172,8261,8351,8437,8520,8585,8651,8731,8979,9336,9414,9480", "endColumns": "89,76,108,97,88,88,89,85,82,64,65,79,83,73,77,65,120", "endOffsets": "3994,4071,7826,7924,8089,8256,8346,8432,8515,8580,8646,8726,8810,9048,9409,9475,9596"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\895477fa5033432c05b79dc01cd2b72e\\transformed\\appcompat-1.6.1\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,424,509,613,724,802,879,970,1063,1155,1249,1349,1442,1537,1633,1724,1815,1896,2003,2107,2205,2308,2412,2516,2673,2772", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "203,306,419,504,608,719,797,874,965,1058,1150,1244,1344,1437,1532,1628,1719,1810,1891,1998,2102,2200,2303,2407,2511,2668,2767,2849"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,311,424,509,613,724,802,879,970,1063,1155,1249,1349,1442,1537,1633,1724,1815,1896,2003,2107,2205,2308,2412,2516,2673,8897", "endColumns": "102,102,112,84,103,110,77,76,90,92,91,93,99,92,94,95,90,90,80,106,103,97,102,103,103,156,98,81", "endOffsets": "203,306,419,504,608,719,797,874,965,1058,1150,1244,1344,1437,1532,1628,1719,1810,1891,1998,2102,2200,2303,2407,2511,2668,2767,8974"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\afd914afebd159dc02db3b680f36ef47\\transformed\\play-services-basement-16.0.1\\res\\values-lo\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "190", "endOffsets": "437"}, "to": {"startLines": "43", "startColumns": "4", "startOffsets": "4153", "endColumns": "190", "endOffsets": "4339"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\caaa22807b29cbcd058e6ca17cad94f2\\transformed\\material3-1.1.2\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,165,273,379,486,563,655,765,890,1004,1131,1212,1308,1394,1490,1604,1720,1821,1944,2065,2191,2337,2454,2564,2679,2788,2875,2970,3079,3200,3290,3389,3491,3613,3743,3849,3941,4017,4095,4177,4259,4359,4441,4524,4623,4721,4812,4911,4993,5090,5184,5281,5404,5486,5582", "endColumns": "109,107,105,106,76,91,109,124,113,126,80,95,85,95,113,115,100,122,120,125,145,116,109,114,108,86,94,108,120,89,98,101,121,129,105,91,75,77,81,81,99,81,82,98,97,90,98,81,96,93,96,122,81,95,90", "endOffsets": "160,268,374,481,558,650,760,885,999,1126,1207,1303,1389,1485,1599,1715,1816,1939,2060,2186,2332,2449,2559,2674,2783,2870,2965,3074,3195,3285,3384,3486,3608,3738,3844,3936,4012,4090,4172,4254,4354,4436,4519,4618,4716,4807,4906,4988,5085,5179,5276,5399,5481,5577,5668"}, "to": {"startLines": "29,30,31,32,42,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,77,79,88,91,93,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2772,2882,2990,3096,4076,4344,4436,4546,4671,4785,4912,4993,5089,5175,5271,5385,5501,5602,5725,5846,5972,6118,6235,6345,6460,6569,6656,6751,6860,6981,7071,7170,7272,7394,7524,7630,7929,8094,8815,9053,9236,9601,9683,9766,9865,9963,10054,10153,10235,10332,10426,10523,10646,10728,10824", "endColumns": "109,107,105,106,76,91,109,124,113,126,80,95,85,95,113,115,100,122,120,125,145,116,109,114,108,86,94,108,120,89,98,101,121,129,105,91,75,77,81,81,99,81,82,98,97,90,98,81,96,93,96,122,81,95,90", "endOffsets": "2877,2985,3091,3198,4148,4431,4541,4666,4780,4907,4988,5084,5170,5266,5380,5496,5597,5720,5841,5967,6113,6230,6340,6455,6564,6651,6746,6855,6976,7066,7165,7267,7389,7519,7625,7717,8000,8167,8892,9130,9331,9678,9761,9860,9958,10049,10148,10230,10327,10421,10518,10641,10723,10819,10910"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e44b6092d301126dd6e126ccf393f4dd\\transformed\\core-1.12.0\\res\\values-lo\\values-lo.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,151,254,353,451,552,650,761", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "146,249,348,446,547,645,756,857"}, "to": {"startLines": "33,34,35,36,37,38,39,92", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3203,3299,3402,3501,3599,3700,3798,9135", "endColumns": "95,102,98,97,100,97,110,100", "endOffsets": "3294,3397,3496,3594,3695,3793,3904,9231"}}]}]}