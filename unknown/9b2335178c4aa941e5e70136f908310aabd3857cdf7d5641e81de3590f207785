package com.arina.ai.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.unit.dp
import com.arina.ai.R
import com.arina.ai.data.database.UserPreferences
import com.arina.ai.ui.components.HolographicCard
import com.arina.ai.ui.theme.ArinaColors

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    preferences: UserPreferences,
    onSave: (UserPreferences) -> Unit,
    onDismiss: () -> Unit,
    modifier: Modifier = Modifier
) {
    var selectedModel by remember { mutableStateOf(preferences.selectedModel) }
    var wakeWordEnabled by remember { mutableStateOf(preferences.wakeWordEnabled) }
    var notificationSpeakerEnabled by remember { mutableStateOf(preferences.notificationSpeakerEnabled) }
    var emotionalResponsesEnabled by remember { mutableStateOf(preferences.emotionalResponsesEnabled) }
    var voiceSpeed by remember { mutableStateOf(preferences.voiceSpeed) }
    var voicePitch by remember { mutableStateOf(preferences.voicePitch) }
    var theme by remember { mutableStateOf(preferences.theme) }

    Scaffold(
        topBar = {
            TopAppBar(
                title = { Text(stringResource(R.string.settings)) },
                navigationIcon = {
                    IconButton(onClick = onDismiss) {
                        Icon(Icons.Default.Close, contentDescription = stringResource(R.string.cancel))
                    }
                },
                actions = {
                    TextButton(
                        onClick = {
                            onSave(
                                preferences.copy(
                                    selectedModel = selectedModel,
                                    wakeWordEnabled = wakeWordEnabled,
                                    notificationSpeakerEnabled = notificationSpeakerEnabled,
                                    emotionalResponsesEnabled = emotionalResponsesEnabled,
                                    voiceSpeed = voiceSpeed,
                                    voicePitch = voicePitch,
                                    theme = theme
                                )
                            )
                        }
                    ) {
                        Text(stringResource(R.string.save))
                    }
                }
            )
        }
    ) { padding ->
        Column(
            modifier = modifier
                .fillMaxSize()
                .padding(padding)
                .verticalScroll(rememberScrollState())
                .padding(16.dp),
            verticalArrangement = Arrangement.spacedBy(16.dp)
        ) {
            // AI Model Selection
            HolographicCard {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    Text(
                        text = stringResource(R.string.ai_model),
                        style = MaterialTheme.typography.titleMedium,
                        color = ArinaColors.NeonCyan
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    OutlinedTextField(
                        value = selectedModel,
                        onValueChange = { selectedModel = it },
                        modifier = Modifier.fillMaxWidth(),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = ArinaColors.NeonCyan,
                            unfocusedBorderColor = ArinaColors.BorderPrimary
                        )
                    )
                }
            }

            // Wake Word Settings
            HolographicCard {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = stringResource(R.string.wake_word),
                            style = MaterialTheme.typography.titleMedium,
                            color = ArinaColors.NeonMagenta
                        )
                        Switch(
                            checked = wakeWordEnabled,
                            onCheckedChange = { wakeWordEnabled = it },
                            colors = SwitchDefaults.colors(
                                checkedThumbColor = ArinaColors.NeonMagenta,
                                checkedTrackColor = ArinaColors.MagentaGlow
                            )
                        )
                    }
                }
            }

            // Notification Settings
            HolographicCard {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = stringResource(R.string.notifications),
                            style = MaterialTheme.typography.titleMedium,
                            color = ArinaColors.NeonPurple
                        )
                        Switch(
                            checked = notificationSpeakerEnabled,
                            onCheckedChange = { notificationSpeakerEnabled = it },
                            colors = SwitchDefaults.colors(
                                checkedThumbColor = ArinaColors.NeonPurple,
                                checkedTrackColor = ArinaColors.VioletGlow
                            )
                        )
                    }
                }
            }

            // Emotional Responses
            HolographicCard {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = stringResource(R.string.emotional_responses),
                            style = MaterialTheme.typography.titleMedium,
                            color = ArinaColors.NeonYellow
                        )
                        Switch(
                            checked = emotionalResponsesEnabled,
                            onCheckedChange = { emotionalResponsesEnabled = it },
                            colors = SwitchDefaults.colors(
                                checkedThumbColor = ArinaColors.NeonYellow,
                                checkedTrackColor = ArinaColors.NeonYellow.copy(alpha = 0.4f)
                            )
                        )
                    }
                }
            }

            // Voice Settings
            HolographicCard {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    Text(
                        text = stringResource(R.string.voice_settings),
                        style = MaterialTheme.typography.titleMedium,
                        color = ArinaColors.NeonBlue
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                    
                    Text(
                        text = stringResource(R.string.voice_speed),
                        style = MaterialTheme.typography.bodyMedium,
                        color = ArinaColors.TextSecondary
                    )
                    Slider(
                        value = voiceSpeed,
                        onValueChange = { voiceSpeed = it },
                        valueRange = 0.5f..2f,
                        colors = SliderDefaults.colors(
                            thumbColor = ArinaColors.NeonBlue,
                            activeTrackColor = ArinaColors.NeonBlue,
                            inactiveTrackColor = ArinaColors.BorderPrimary
                        )
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = stringResource(R.string.voice_pitch),
                        style = MaterialTheme.typography.bodyMedium,
                        color = ArinaColors.TextSecondary
                    )
                    Slider(
                        value = voicePitch,
                        onValueChange = { voicePitch = it },
                        valueRange = 0.5f..2f,
                        colors = SliderDefaults.colors(
                            thumbColor = ArinaColors.NeonBlue,
                            activeTrackColor = ArinaColors.NeonBlue,
                            inactiveTrackColor = ArinaColors.BorderPrimary
                        )
                    )
                }
            }

            // Theme Selection
            HolographicCard {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(16.dp)
                ) {
                    Text(
                        text = stringResource(R.string.theme),
                        style = MaterialTheme.typography.titleMedium,
                        color = ArinaColors.NeonGreen
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    OutlinedTextField(
                        value = theme,
                        onValueChange = { theme = it },
                        modifier = Modifier.fillMaxWidth(),
                        colors = OutlinedTextFieldDefaults.colors(
                            focusedBorderColor = ArinaColors.NeonGreen,
                            unfocusedBorderColor = ArinaColors.BorderPrimary
                        )
                    )
                }
            }
        }
    }
}