package com.arina.ai

import android.Manifest
import android.app.admin.DevicePolicyManager
import android.content.ComponentName
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Bundle
import android.speech.tts.TextToSpeech
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.core.content.ContextCompat
import com.arina.ai.data.api.A4FService
import com.arina.ai.data.database.ArinaDatabase
import com.arina.ai.data.repository.ArinaRepository
import com.arina.ai.receiver.ArinaDeviceAdminReceiver
import com.arina.ai.service.SystemControlService
import com.arina.ai.service.VoiceInteractionService
import com.arina.ai.ui.screens.MainScreen
import com.arina.ai.ui.theme.ArinaTheme
import com.arina.ai.viewmodel.MainViewModel
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.*

class MainActivity : ComponentActivity() {
    private lateinit var viewModel: MainViewModel
    private lateinit var textToSpeech: TextToSpeech
    private lateinit var systemControlService: SystemControlService
    private lateinit var voiceInteractionService: VoiceInteractionService

    private val requiredPermissions = arrayOf(
        Manifest.permission.RECORD_AUDIO,
        Manifest.permission.CAMERA,
        Manifest.permission.CALL_PHONE,
        Manifest.permission.READ_PHONE_STATE,
        Manifest.permission.ANSWER_PHONE_CALLS,
        Manifest.permission.SYSTEM_ALERT_WINDOW,
        Manifest.permission.POST_NOTIFICATIONS,
        Manifest.permission.WRITE_SETTINGS,
        Manifest.permission.BLUETOOTH_ADMIN,
        Manifest.permission.MODIFY_AUDIO_SETTINGS,
        Manifest.permission.READ_EXTERNAL_STORAGE,
        Manifest.permission.WRITE_EXTERNAL_STORAGE
    )

    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        val allGranted = permissions.entries.all { it.value }
        if (allGranted) {
            initializeApp()
        } else {
            // Handle permission denial
            finish()
        }
    }

    private val deviceAdminLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (isDeviceAdminActive()) {
            initializeApp()
        } else {
            // Handle device admin denial
            finish()
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        if (hasRequiredPermissions()) {
            requestDeviceAdmin()
        } else {
            permissionLauncher.launch(requiredPermissions)
        }
    }

    private fun hasRequiredPermissions(): Boolean {
        return requiredPermissions.all { permission ->
            ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED
        }
    }

    private fun requestDeviceAdmin() {
        if (isDeviceAdminActive()) {
            initializeApp()
        } else {
            val intent = Intent(DevicePolicyManager.ACTION_ADD_DEVICE_ADMIN).apply {
                putExtra(DevicePolicyManager.EXTRA_DEVICE_ADMIN, ComponentName(this@MainActivity, ArinaDeviceAdminReceiver::class.java))
                putExtra(DevicePolicyManager.EXTRA_ADD_EXPLANATION, "ARINA needs device admin access for advanced system controls")
            }
            deviceAdminLauncher.launch(intent)
        }
    }

    private fun isDeviceAdminActive(): Boolean {
        val devicePolicyManager = getSystemService(DEVICE_POLICY_SERVICE) as DevicePolicyManager
        val adminComponent = ComponentName(this, ArinaDeviceAdminReceiver::class.java)
        return devicePolicyManager.isAdminActive(adminComponent)
    }

    private fun initializeApp() {
        // Initialize TextToSpeech
        textToSpeech = TextToSpeech(this) { status ->
            if (status == TextToSpeech.SUCCESS) {
                textToSpeech.language = Locale.US
            }
        }

        // Initialize SystemControlService
        systemControlService = SystemControlService(applicationContext)

        // Initialize VoiceInteractionService
        voiceInteractionService = VoiceInteractionService(applicationContext)

        // Initialize Retrofit
        val retrofit = Retrofit.Builder()
            .baseUrl("https://api.a4f.co/")
            .addConverterFactory(GsonConverterFactory.create())
            .build()

        val apiService = retrofit.create(A4FService::class.java)
        
        // Initialize Database
        val database = ArinaDatabase.getInstance(applicationContext)
        
        // Initialize Repository
        val repository = ArinaRepository(database, apiService)
        
        // Initialize ViewModel
        viewModel = MainViewModel(repository, textToSpeech, systemControlService).apply {
            // Set up voice interaction callback
            voiceInteractionService.setVoiceResultCallback { text ->
                handleEvent(MainViewEvent.ProcessVoiceInput(text))
            }
        }

        // Set up the UI
        setContent {
            ArinaTheme {
                MainScreen(
                    viewModel = viewModel,
                    voiceInteractionService = voiceInteractionService,
                    modifier = Modifier.fillMaxSize()
                )
            }
        }

        // Start services
        startService(Intent(this, com.arina.ai.service.ArinaNotificationService::class.java))
        voiceInteractionService.startWakeWordDetection()
    }

    override fun onDestroy() {
        super.onDestroy()
        if (::textToSpeech.isInitialized) {
            textToSpeech.shutdown()
        }
        if (::voiceInteractionService.isInitialized) {
            voiceInteractionService.release()
        }
    }
} 