package com.arina.ai.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import com.arina.ai.data.database.UserPreferences

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsDialog(
    preferences: UserPreferences?,
    onDismiss: () -> Unit,
    onSave: (UserPreferences) -> Unit
) {
    var selectedModel by remember { mutableStateOf(preferences?.selectedModel ?: "gpt-4") }
    var voiceSpeed by remember { mutableStateOf(preferences?.voiceSpeed ?: 1.0f) }
    var voicePitch by remember { mutableStateOf(preferences?.voicePitch ?: 1.0f) }
    var notificationSpeakerEnabled by remember { mutableStateOf(preferences?.notificationSpeakerEnabled ?: false) }
    var wakeWord by remember { mutableStateOf(preferences?.wakeWord ?: "Hey <PERSON><PERSON>") }

    AlertDialog(
        onDismissRequest = onDismiss,
        title = { Text("ARINA Settings") },
        text = {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(vertical = 16.dp),
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // AI Model Selection
                OutlinedTextField(
                    value = selectedModel,
                    onValueChange = { selectedModel = it },
                    label = { Text("AI Model") },
                    modifier = Modifier.fillMaxWidth()
                )

                // Voice Speed Slider
                Text("Voice Speed")
                Slider(
                    value = voiceSpeed,
                    onValueChange = { voiceSpeed = it },
                    valueRange = 0.5f..2.0f,
                    steps = 5
                )
                Text("${String.format("%.1f", voiceSpeed)}x")

                // Voice Pitch Slider
                Text("Voice Pitch")
                Slider(
                    value = voicePitch,
                    onValueChange = { voicePitch = it },
                    valueRange = 0.5f..2.0f,
                    steps = 5
                )
                Text("${String.format("%.1f", voicePitch)}x")

                // Wake Word Input
                OutlinedTextField(
                    value = wakeWord,
                    onValueChange = { wakeWord = it },
                    label = { Text("Wake Word") },
                    modifier = Modifier.fillMaxWidth()
                )

                // Notification Speaker Toggle
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text("Speak Notifications")
                    Switch(
                        checked = notificationSpeakerEnabled,
                        onCheckedChange = { notificationSpeakerEnabled = it }
                    )
                }
            }
        },
        confirmButton = {
            TextButton(
                onClick = {
                    onSave(
                        UserPreferences(
                            id = preferences?.id ?: 0,
                            selectedModel = selectedModel,
                            voiceSpeed = voiceSpeed,
                            voicePitch = voicePitch,
                            wakeWord = wakeWord,
                            notificationSpeakerEnabled = notificationSpeakerEnabled
                        )
                    )
                }
            ) {
                Text("Save")
            }
        },
        dismissButton = {
            TextButton(onClick = onDismiss) {
                Text("Cancel")
            }
        }
    )
} 