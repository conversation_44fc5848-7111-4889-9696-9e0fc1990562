package com.arina.ai.service;

@javax.inject.Singleton
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000V\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\u000b\n\u0002\b\u0004\n\u0002\u0010\u000e\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\r\b\u0007\u0018\u00002\u00020\u0001BA\b\u0007\u0012\b\b\u0001\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u0012\u0006\u0010\b\u001a\u00020\t\u0012\u0006\u0010\n\u001a\u00020\u000b\u0012\u0006\u0010\f\u001a\u00020\r\u0012\u0006\u0010\u000e\u001a\u00020\u000f\u00a2\u0006\u0002\u0010\u0010J\u0006\u0010\u0011\u001a\u00020\u0012J\u0006\u0010\u0013\u001a\u00020\u0012J\u0006\u0010\u0014\u001a\u00020\u0015J\u0006\u0010\u0016\u001a\u00020\u0015J\u0006\u0010\u0017\u001a\u00020\u0012J\u000e\u0010\u0018\u001a\u00020\u00122\u0006\u0010\u0019\u001a\u00020\u001aJ\u000e\u0010\u001b\u001a\u00020\u00122\u0006\u0010\u001c\u001a\u00020\u001dJ\u000e\u0010\u001e\u001a\u00020\u00122\u0006\u0010\u001f\u001a\u00020\u001aJ\u000e\u0010 \u001a\u00020\u00122\u0006\u0010!\u001a\u00020\u0015J\u000e\u0010\"\u001a\u00020\u00122\u0006\u0010#\u001a\u00020\u001dJ\u000e\u0010$\u001a\u00020\u00122\u0006\u0010%\u001a\u00020\u001dJ\u0016\u0010&\u001a\u00020\u00122\u0006\u0010\u001c\u001a\u00020\u001d2\u0006\u0010\'\u001a\u00020\u001dJ\u000e\u0010(\u001a\u00020\u00122\u0006\u0010!\u001a\u00020\u0015J\u000e\u0010)\u001a\u00020\u00122\u0006\u0010\u001c\u001a\u00020\u001dR\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\f\u001a\u00020\rX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u000e\u001a\u00020\u000fX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\n\u001a\u00020\u000bX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006*"}, d2 = {"Lcom/arina/ai/service/SystemControlService;", "", "context", "Landroid/content/Context;", "devicePolicyManager", "Landroid/app/admin/DevicePolicyManager;", "powerManager", "Landroid/os/PowerManager;", "audioManager", "Landroid/media/AudioManager;", "wifiManager", "Landroid/net/wifi/WifiManager;", "bluetoothManager", "Landroid/bluetooth/BluetoothManager;", "telecomManager", "Landroid/telecom/TelecomManager;", "(Landroid/content/Context;Landroid/app/admin/DevicePolicyManager;Landroid/os/PowerManager;Landroid/media/AudioManager;Landroid/net/wifi/WifiManager;Landroid/bluetooth/BluetoothManager;Landroid/telecom/TelecomManager;)V", "answerCall", "", "endCall", "isDeviceIdle", "", "isScreenOn", "lockScreen", "makeCall", "phoneNumber", "", "muteAudio", "streamType", "", "openSettings", "settingsAction", "setBluetoothEnabled", "enabled", "setScreenBrightness", "brightness", "setScreenTimeout", "timeoutMs", "setVolume", "volume", "setWifiEnabled", "unmuteAudio", "app_debug"})
public final class SystemControlService {
    @org.jetbrains.annotations.NotNull
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull
    private final android.app.admin.DevicePolicyManager devicePolicyManager = null;
    @org.jetbrains.annotations.NotNull
    private final android.os.PowerManager powerManager = null;
    @org.jetbrains.annotations.NotNull
    private final android.media.AudioManager audioManager = null;
    @org.jetbrains.annotations.NotNull
    private final android.net.wifi.WifiManager wifiManager = null;
    @org.jetbrains.annotations.NotNull
    private final android.bluetooth.BluetoothManager bluetoothManager = null;
    @org.jetbrains.annotations.NotNull
    private final android.telecom.TelecomManager telecomManager = null;
    
    @javax.inject.Inject
    public SystemControlService(@dagger.hilt.android.qualifiers.ApplicationContext
    @org.jetbrains.annotations.NotNull
    android.content.Context context, @org.jetbrains.annotations.NotNull
    android.app.admin.DevicePolicyManager devicePolicyManager, @org.jetbrains.annotations.NotNull
    android.os.PowerManager powerManager, @org.jetbrains.annotations.NotNull
    android.media.AudioManager audioManager, @org.jetbrains.annotations.NotNull
    android.net.wifi.WifiManager wifiManager, @org.jetbrains.annotations.NotNull
    android.bluetooth.BluetoothManager bluetoothManager, @org.jetbrains.annotations.NotNull
    android.telecom.TelecomManager telecomManager) {
        super();
    }
    
    public final void setScreenBrightness(int brightness) {
    }
    
    public final void setScreenTimeout(int timeoutMs) {
    }
    
    public final void lockScreen() {
    }
    
    public final void setVolume(int streamType, int volume) {
    }
    
    public final void muteAudio(int streamType) {
    }
    
    public final void unmuteAudio(int streamType) {
    }
    
    public final void setWifiEnabled(boolean enabled) {
    }
    
    public final void setBluetoothEnabled(boolean enabled) {
    }
    
    public final void answerCall() {
    }
    
    public final void endCall() {
    }
    
    public final void makeCall(@org.jetbrains.annotations.NotNull
    java.lang.String phoneNumber) {
    }
    
    public final boolean isDeviceIdle() {
        return false;
    }
    
    public final boolean isScreenOn() {
        return false;
    }
    
    public final void openSettings(@org.jetbrains.annotations.NotNull
    java.lang.String settingsAction) {
    }
}