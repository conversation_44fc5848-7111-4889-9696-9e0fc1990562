package com.arina.ai.service;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000H\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0005\n\u0002\u0018\u0002\n\u0002\u0010\u000e\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\b\r\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\u0010\u0010\u001d\u001a\u00020\u00132\u0006\u0010\u001e\u001a\u00020\u0012H\u0002J\u0006\u0010\u001f\u001a\u00020\u0013J\u001a\u0010 \u001a\u00020\u00132\u0012\u0010!\u001a\u000e\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u00130\u0011J\b\u0010\"\u001a\u00020\u0013H\u0002J\u0006\u0010#\u001a\u00020\u0013J\u0006\u0010$\u001a\u00020\u0013J\u0006\u0010%\u001a\u00020\u0013R\u0014\u0010\u0005\u001a\b\u0012\u0004\u0012\u00020\u00070\u0006X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\b\u001a\u00020\tX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001b\u0010\n\u001a\u00020\u000b8BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u000e\u0010\u000f\u001a\u0004\b\f\u0010\rR\u001c\u0010\u0010\u001a\u0010\u0012\u0004\u0012\u00020\u0012\u0012\u0004\u0012\u00020\u0013\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000R\u0017\u0010\u0014\u001a\b\u0012\u0004\u0012\u00020\u00070\u0015\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0017R\u001b\u0010\u0018\u001a\u00020\u00198BX\u0082\u0084\u0002\u00a2\u0006\f\n\u0004\b\u001c\u0010\u000f\u001a\u0004\b\u001a\u0010\u001b\u00a8\u0006&"}, d2 = {"Lcom/arina/ai/service/VoiceInteractionService;", "", "context", "Landroid/content/Context;", "(Landroid/content/Context;)V", "_voiceState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/arina/ai/service/VoiceState;", "serviceScope", "Lkotlinx/coroutines/CoroutineScope;", "speechRecognizer", "Lcom/arina/ai/service/SpeechRecognizer;", "getSpeechRecognizer", "()Lcom/arina/ai/service/SpeechRecognizer;", "speechRecognizer$delegate", "Lkotlin/Lazy;", "voiceResultCallback", "Lkotlin/Function1;", "", "", "voiceState", "Lkotlinx/coroutines/flow/StateFlow;", "getVoiceState", "()Lkotlinx/coroutines/flow/StateFlow;", "wakeWordDetector", "Lcom/arina/ai/service/WakeWordDetector;", "getWakeWordDetector", "()Lcom/arina/ai/service/WakeWordDetector;", "wakeWordDetector$delegate", "processCommand", "text", "release", "setVoiceResultCallback", "callback", "startListening", "startManualListening", "startWakeWordDetection", "stopListening", "app_debug"})
public final class VoiceInteractionService {
    @org.jetbrains.annotations.NotNull
    private final android.content.Context context = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.CoroutineScope serviceScope = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<com.arina.ai.service.VoiceState> _voiceState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.arina.ai.service.VoiceState> voiceState = null;
    @org.jetbrains.annotations.Nullable
    private kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> voiceResultCallback;
    @org.jetbrains.annotations.NotNull
    private final kotlin.Lazy wakeWordDetector$delegate = null;
    @org.jetbrains.annotations.NotNull
    private final kotlin.Lazy speechRecognizer$delegate = null;
    
    public VoiceInteractionService(@org.jetbrains.annotations.NotNull
    android.content.Context context) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.arina.ai.service.VoiceState> getVoiceState() {
        return null;
    }
    
    private final com.arina.ai.service.WakeWordDetector getWakeWordDetector() {
        return null;
    }
    
    private final com.arina.ai.service.SpeechRecognizer getSpeechRecognizer() {
        return null;
    }
    
    public final void setVoiceResultCallback(@org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super java.lang.String, kotlin.Unit> callback) {
    }
    
    public final void startWakeWordDetection() {
    }
    
    private final void startListening() {
    }
    
    private final void processCommand(java.lang.String text) {
    }
    
    public final void startManualListening() {
    }
    
    public final void stopListening() {
    }
    
    public final void release() {
    }
}