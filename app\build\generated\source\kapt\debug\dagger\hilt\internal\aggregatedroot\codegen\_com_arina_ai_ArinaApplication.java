package dagger.hilt.internal.aggregatedroot.codegen;

import dagger.hilt.android.HiltAndroidApp;
import dagger.hilt.internal.aggregatedroot.AggregatedRoot;
import javax.annotation.processing.Generated;

/**
 * This class should only be referenced by generated code! This class aggregates information across multiple compilations.
 */
@AggregatedRoot(
    root = "com.arina.ai.ArinaApplication",
    rootPackage = "com.arina.ai",
    originatingRoot = "com.arina.ai.ArinaApplication",
    originatingRootPackage = "com.arina.ai",
    rootAnnotation = HiltAndroidApp.class,
    rootSimpleNames = "ArinaApplication",
    originatingRootSimpleNames = "ArinaApplication"
)
@Generated("dagger.hilt.processor.internal.root.AggregatedRootGenerator")
public class _com_arina_ai_ArinaApplication {
}
