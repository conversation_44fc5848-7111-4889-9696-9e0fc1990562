[{"merged": "com.arina.ai.app-merged_res-66:/drawable_ic_launcher.xml.flat", "source": "com.arina.ai.app-main-68:/drawable/ic_launcher.xml"}, {"merged": "com.arina.ai.app-merged_res-66:/drawable_ic_launcher_foreground.xml.flat", "source": "com.arina.ai.app-main-68:/drawable/ic_launcher_foreground.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.arina.ai.app-merged_res-66:\\xml_device_admin.xml.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.arina.ai.app-main-68:\\xml\\device_admin.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.arina.ai.app-merged_res-66:\\raw_arina_excited.json.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.arina.ai.app-main-68:\\raw\\arina_excited.json"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.arina.ai.app-merged_res-66:\\raw_arina_confused.json.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.arina.ai.app-main-68:\\raw\\arina_confused.json"}, {"merged": "com.arina.ai.app-merged_res-66:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.arina.ai.app-main-68:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.arina.ai.app-merged_res-66:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.arina.ai.app-main-68:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.arina.ai.app-merged_res-66:/xml_backup_rules.xml.flat", "source": "com.arina.ai.app-main-68:/xml/backup_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.arina.ai.app-merged_res-66:\\raw_arina_calm.json.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.arina.ai.app-main-68:\\raw\\arina_calm.json"}, {"merged": "com.arina.ai.app-merged_res-66:/drawable_ic_launcher_background.xml.flat", "source": "com.arina.ai.app-main-68:/drawable/ic_launcher_background.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.arina.ai.app-merged_res-66:\\raw_arina_concerned.json.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.arina.ai.app-main-68:\\raw\\arina_concerned.json"}, {"merged": "com.arina.ai.app-merged_res-66:/xml_data_extraction_rules.xml.flat", "source": "com.arina.ai.app-main-68:/xml/data_extraction_rules.xml"}, {"merged": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.arina.ai.app-merged_res-66:\\raw_arina_focused.json.flat", "source": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.arina.ai.app-main-68:\\raw\\arina_focused.json"}]