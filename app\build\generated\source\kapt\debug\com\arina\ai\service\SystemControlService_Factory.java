package com.arina.ai.service;

import android.app.admin.DevicePolicyManager;
import android.bluetooth.BluetoothManager;
import android.content.Context;
import android.media.AudioManager;
import android.net.wifi.WifiManager;
import android.os.PowerManager;
import android.telecom.TelecomManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SystemControlService_Factory implements Factory<SystemControlService> {
  private final Provider<Context> contextProvider;

  private final Provider<DevicePolicyManager> devicePolicyManagerProvider;

  private final Provider<PowerManager> powerManagerProvider;

  private final Provider<AudioManager> audioManagerProvider;

  private final Provider<WifiManager> wifiManagerProvider;

  private final Provider<BluetoothManager> bluetoothManagerProvider;

  private final Provider<TelecomManager> telecomManagerProvider;

  public SystemControlService_Factory(Provider<Context> contextProvider,
      Provider<DevicePolicyManager> devicePolicyManagerProvider,
      Provider<PowerManager> powerManagerProvider, Provider<AudioManager> audioManagerProvider,
      Provider<WifiManager> wifiManagerProvider,
      Provider<BluetoothManager> bluetoothManagerProvider,
      Provider<TelecomManager> telecomManagerProvider) {
    this.contextProvider = contextProvider;
    this.devicePolicyManagerProvider = devicePolicyManagerProvider;
    this.powerManagerProvider = powerManagerProvider;
    this.audioManagerProvider = audioManagerProvider;
    this.wifiManagerProvider = wifiManagerProvider;
    this.bluetoothManagerProvider = bluetoothManagerProvider;
    this.telecomManagerProvider = telecomManagerProvider;
  }

  @Override
  public SystemControlService get() {
    return newInstance(contextProvider.get(), devicePolicyManagerProvider.get(), powerManagerProvider.get(), audioManagerProvider.get(), wifiManagerProvider.get(), bluetoothManagerProvider.get(), telecomManagerProvider.get());
  }

  public static SystemControlService_Factory create(Provider<Context> contextProvider,
      Provider<DevicePolicyManager> devicePolicyManagerProvider,
      Provider<PowerManager> powerManagerProvider, Provider<AudioManager> audioManagerProvider,
      Provider<WifiManager> wifiManagerProvider,
      Provider<BluetoothManager> bluetoothManagerProvider,
      Provider<TelecomManager> telecomManagerProvider) {
    return new SystemControlService_Factory(contextProvider, devicePolicyManagerProvider, powerManagerProvider, audioManagerProvider, wifiManagerProvider, bluetoothManagerProvider, telecomManagerProvider);
  }

  public static SystemControlService newInstance(Context context,
      DevicePolicyManager devicePolicyManager, PowerManager powerManager, AudioManager audioManager,
      WifiManager wifiManager, BluetoothManager bluetoothManager, TelecomManager telecomManager) {
    return new SystemControlService(context, devicePolicyManager, powerManager, audioManager, wifiManager, bluetoothManager, telecomManager);
  }
}
