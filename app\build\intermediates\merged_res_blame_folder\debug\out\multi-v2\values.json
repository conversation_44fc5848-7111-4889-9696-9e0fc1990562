{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.2\\com.arina.ai.app-mergeDebugResources-64:\\values\\values.xml", "map": [{"source": "C:\\Users\\<USER>\\Desktop\\Arina\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "91", "endLines": "17", "endColumns": "12", "endOffsets": "760"}, "to": {"startLines": "1846", "startColumns": "4", "startOffsets": "122106", "endLines": "1860", "endColumns": "12", "endOffsets": "122658"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d9b68a39eaa82fe8f489e3031923adb9\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,236,237,238,240,242,276,325,326,364,365,368,377,378,381,382,386,387,391,392,397,407,408,410,1518,1521,1524", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13588,13647,13706,13766,13826,13886,13946,14006,14066,14126,14186,14246,14306,14365,14425,14485,14545,14605,14665,14725,14785,14845,14905,14965,15024,15084,15144,15203,15262,15321,15380,15439,15703,15777,15835,15947,16032,17789,21137,21202,24007,24073,24332,24920,24972,25150,25212,25490,25526,25790,25840,26078,26612,26659,26749,97408,97520,97631", "endLines": "200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,236,237,238,240,242,276,325,326,364,365,368,377,378,381,382,386,387,391,392,397,407,408,410,1520,1523,1527", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "13642,13701,13761,13821,13881,13941,14001,14061,14121,14181,14241,14301,14360,14420,14480,14540,14600,14660,14720,14780,14840,14900,14960,15019,15079,15139,15198,15257,15316,15375,15434,15493,15772,15830,15885,15993,16082,17837,21197,21251,24068,24169,24385,24967,25027,25207,25261,25521,25555,25835,25889,26119,26654,26690,26834,97515,97626,97821"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c659bea7b1d60839c0336fc95ad688cd\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "271", "startColumns": "4", "startOffsets": "17518", "endColumns": "42", "endOffsets": "17556"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e048f096732ed3286ecb33d88bdc8bcb\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "251,272", "startColumns": "4,4", "startOffsets": "16474,17561", "endColumns": "41,59", "endOffsets": "16511,17616"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8d0f68a3cbcc70041c66c54f5b861c50\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "311", "startColumns": "4", "startOffsets": "20135", "endColumns": "82", "endOffsets": "20213"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\aa8b3d3e2faa2b54bbfdbc227ec2daf1\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "274", "startColumns": "4", "startOffsets": "17675", "endColumns": "49", "endOffsets": "17720"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\358e273e9a2417ed24f02c311ea2ead3\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "239,252,275", "startColumns": "4,4,4", "startOffsets": "15890,16516,17725", "endColumns": "56,64,63", "endOffsets": "15942,16576,17784"}}, {"source": "C:\\Users\\<USER>\\Desktop\\Arina\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "310,312,324,366,369,370,371,372,373,374,375,379,383,384,385,388,389,390,393,394,398,400,401,402,403,404,409,411,426,427,428,429", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20089,20218,21095,24174,24390,24458,24516,24598,24667,24728,24798,25032,25266,25364,25434,25560,25639,25739,25894,25934,26124,26223,26267,26321,26373,26423,26695,26839,27758,27810,27868,27920", "endColumns": "45,42,41,113,67,57,81,68,60,69,75,48,97,69,55,78,99,50,39,37,45,43,53,51,49,49,53,39,51,57,51,47", "endOffsets": "20130,20256,21132,24283,24453,24511,24593,24662,24723,24793,24869,25076,25359,25429,25485,25634,25734,25785,25929,25967,26165,26262,26316,26368,26418,26468,26744,26874,27805,27863,27915,27963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\be4d7956f63fad6a2908a9518ac09c51\\transformed\\lottie-6.3.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "5,246", "startColumns": "4,4", "startOffsets": "299,16211", "endColumns": "62,46", "endOffsets": "357,16253"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\caaa22807b29cbcd058e6ca17cad94f2\\transformed\\material3-1.1.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "313,314,315,316,327,329,330,331,332,333,336,337,338,339,340,341,342,343,344,345,346,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,367,376,380,395,399,406,412,413,414,415,416,417,418,419,420,421,422,423,424,425", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20261,20345,20427,20504,21256,21471,21532,21611,21713,21795,21911,21961,22026,22083,22148,22233,22324,22394,22487,22576,22670,22815,22902,22986,23078,23172,23232,23296,23379,23469,23532,23600,23668,23765,23870,23942,24288,24874,25081,25972,26170,26544,26879,26925,26975,27042,27109,27175,27240,27294,27366,27433,27503,27585,27631,27697", "endLines": "313,314,315,316,327,329,330,331,332,335,336,337,338,339,340,341,342,343,344,345,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,367,376,380,395,399,406,412,413,414,415,416,417,418,419,420,421,422,423,424,425", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "20340,20422,20499,20579,21299,21527,21606,21708,21790,21906,21956,22021,22078,22143,22228,22319,22389,22482,22571,22665,22810,22897,22981,23073,23167,23227,23291,23374,23464,23527,23595,23663,23760,23865,23937,24002,24327,24915,25145,26020,26218,26607,26920,26970,27037,27104,27170,27235,27289,27361,27428,27498,27580,27626,27692,27753"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5b15d10bbf71c8d5b5d51f157942f826\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "243,248", "startColumns": "4,4", "startOffsets": "16087,16311", "endColumns": "53,66", "endOffsets": "16136,16373"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e44b6092d301126dd6e126ccf393f4dd\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "6,17,18,31,32,55,56,158,159,160,161,162,163,164,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,197,198,199,244,245,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,282,317,318,319,320,321,322,323,405,1799,1800,1804,1805,1809,1968,1969", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "362,1081,1153,2201,2266,3685,3754,10672,10742,10810,10882,10952,11013,11087,11944,12005,12066,12128,12192,12254,12315,12383,12483,12543,12609,12682,12751,12808,12860,13375,13447,13523,16141,16176,16627,16682,16745,16800,16858,16916,16977,17040,17097,17148,17198,17259,17316,17382,17416,17451,18153,20584,20651,20723,20792,20861,20935,21007,26473,118712,118829,119030,119140,119341,131322,131394", "endLines": "6,17,18,31,32,55,56,158,159,160,161,162,163,164,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,197,198,199,244,245,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,282,317,318,319,320,321,322,323,405,1799,1803,1804,1808,1809,1968,1969", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "417,1148,1236,2261,2327,3749,3812,10737,10805,10877,10947,11008,11082,11155,12000,12061,12123,12187,12249,12310,12378,12478,12538,12604,12677,12746,12803,12855,12917,13442,13518,13583,16171,16206,16677,16740,16795,16853,16911,16972,17035,17092,17143,17193,17254,17311,17377,17411,17446,17481,18218,20646,20718,20787,20856,20930,21002,21090,26539,118824,119025,119135,119336,119465,131389,131456"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\afd914afebd159dc02db3b680f36ef47\\transformed\\play-services-basement-16.0.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "281,328", "startColumns": "4,4", "startOffsets": "18085,21304", "endColumns": "67,166", "endOffsets": "18148,21466"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\222459b6241d56f6f6360aa54805a23e\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "273", "startColumns": "4", "startOffsets": "17621", "endColumns": "53", "endOffsets": "17670"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\895477fa5033432c05b79dc01cd2b72e\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,7,8,9,10,11,12,13,14,15,16,19,20,21,22,23,24,25,26,27,28,29,30,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,165,166,167,168,169,170,171,172,173,189,190,191,192,193,194,195,196,232,233,234,235,241,249,250,253,270,277,278,279,280,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,396,430,431,432,433,434,435,443,444,448,452,456,461,467,474,478,482,487,491,495,499,503,507,511,517,521,527,531,537,541,546,550,553,557,563,567,573,577,583,586,590,594,598,602,606,607,608,609,612,615,618,621,625,626,627,628,629,632,634,636,638,643,644,648,654,658,659,661,673,674,678,684,688,689,690,694,721,725,726,730,758,930,956,1127,1153,1184,1192,1198,1214,1236,1241,1246,1256,1265,1274,1278,1285,1304,1311,1312,1321,1324,1327,1331,1335,1339,1342,1343,1348,1353,1363,1368,1375,1381,1382,1385,1389,1394,1396,1398,1401,1404,1406,1410,1413,1420,1423,1426,1430,1432,1436,1438,1440,1442,1446,1454,1462,1474,1480,1489,1492,1503,1506,1507,1512,1513,1528,1597,1667,1668,1678,1687,1688,1690,1694,1697,1700,1703,1706,1709,1712,1715,1719,1722,1725,1728,1732,1735,1739,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1765,1767,1768,1769,1770,1771,1772,1773,1774,1776,1777,1779,1780,1782,1784,1785,1787,1788,1789,1790,1791,1792,1794,1795,1796,1797,1798,1810,1812,1814,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1830,1831,1832,1833,1834,1835,1836,1838,1842,1861,1862,1863,1864,1865,1866,1870,1871,1872,1873,1875,1877,1879,1881,1883,1884,1885,1886,1888,1890,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1906,1907,1908,1909,1911,1913,1914,1916,1917,1919,1921,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1936,1937,1938,1939,1941,1942,1943,1944,1945,1947,1949,1951,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,250,422,463,518,580,644,714,775,850,926,1003,1241,1326,1408,1484,1560,1637,1715,1821,1927,2006,2086,2143,2332,2406,2481,2546,2612,2672,2733,2805,2878,2945,3013,3072,3131,3190,3249,3308,3362,3416,3469,3523,3577,3631,3817,3891,3970,4043,4117,4188,4260,4332,4405,4462,4520,4593,4667,4741,4816,4888,4961,5031,5102,5162,5223,5292,5361,5431,5505,5581,5645,5722,5798,5875,5940,6009,6086,6161,6230,6298,6375,6441,6502,6599,6664,6733,6832,6903,6962,7020,7077,7136,7200,7271,7343,7415,7487,7559,7626,7694,7762,7821,7884,7948,8038,8129,8189,8255,8322,8388,8458,8522,8575,8642,8703,8770,8883,8941,9004,9069,9134,9209,9282,9354,9398,9445,9491,9540,9601,9662,9723,9785,9849,9913,9977,10042,10105,10165,10226,10292,10351,10411,10473,10544,10604,11160,11246,11333,11423,11510,11598,11680,11763,11853,12922,12974,13032,13077,13143,13207,13264,13321,15498,15555,15603,15652,15998,16378,16425,16581,17486,17842,17906,17968,18028,18223,18297,18367,18445,18499,18569,18654,18702,18748,18809,18872,18938,19002,19073,19136,19201,19265,19326,19387,19439,19512,19586,19655,19730,19804,19878,20019,26025,27968,28046,28136,28224,28320,28410,28992,29081,29328,29609,29861,30146,30539,31016,31238,31460,31736,31963,32193,32423,32653,32883,33110,33529,33755,34180,34410,34838,35057,35340,35548,35679,35906,36332,36557,36984,37205,37630,37750,38026,38327,38651,38942,39256,39393,39524,39629,39871,40038,40242,40450,40721,40833,40945,41050,41167,41381,41527,41667,41753,42101,42189,42435,42853,43102,43184,43282,43939,44039,44291,44715,44970,45064,45153,45390,47414,47656,47758,48011,50167,60848,62364,73059,74587,76344,76970,77390,78651,79916,80172,80408,80955,81449,82054,82252,82832,84200,84575,84693,85231,85388,85584,85857,86113,86283,86424,86488,86853,87220,87896,88160,88498,88851,88945,89131,89437,89699,89824,89951,90190,90401,90520,90713,90890,91345,91526,91648,91907,92020,92207,92309,92416,92545,92820,93328,93824,94701,94995,95565,95714,96446,96618,96702,97038,97130,97826,103057,108428,108490,109068,109652,109743,109856,110085,110245,110397,110568,110734,110903,111070,111233,111476,111646,111819,111990,112264,112463,112668,112998,113082,113178,113274,113372,113472,113574,113676,113778,113880,113982,114082,114178,114290,114419,114542,114673,114804,114902,115016,115110,115250,115384,115480,115592,115692,115808,115904,116016,116116,116256,116392,116556,116686,116844,116994,117135,117279,117414,117526,117676,117804,117932,118068,118200,118330,118460,118572,119470,119616,119760,119898,119964,120054,120130,120234,120324,120426,120534,120642,120742,120822,120914,121012,121122,121174,121252,121358,121450,121554,121664,121786,121949,122663,122743,122843,122933,123043,123133,123374,123468,123574,123666,123766,123878,123992,124108,124224,124318,124432,124544,124646,124766,124888,124970,125074,125194,125320,125418,125512,125600,125712,125828,125950,126062,126237,126353,126439,126531,126643,126767,126834,126960,127028,127156,127300,127428,127497,127592,127707,127820,127919,128028,128139,128250,128351,128456,128556,128686,128777,128900,128994,129106,129192,129296,129392,129480,129598,129702,129806,129932,130020,130128,130228,130318,130428,130512,130614,130698,130752,130816,130922,131008,131118,131202", "endLines": "2,3,4,7,8,9,10,11,12,13,14,15,16,19,20,21,22,23,24,25,26,27,28,29,30,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,165,166,167,168,169,170,171,172,173,189,190,191,192,193,194,195,196,232,233,234,235,241,249,250,253,270,277,278,279,280,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,396,430,431,432,433,434,442,443,447,451,455,460,466,473,477,481,486,490,494,498,502,506,510,516,520,526,530,536,540,545,549,552,556,562,566,572,576,582,585,589,593,597,601,605,606,607,608,611,614,617,620,624,625,626,627,628,631,633,635,637,642,643,647,653,657,658,660,672,673,677,683,687,688,689,693,720,724,725,729,757,929,955,1126,1152,1183,1191,1197,1213,1235,1240,1245,1255,1264,1273,1277,1284,1303,1310,1311,1320,1323,1326,1330,1334,1338,1341,1342,1347,1352,1362,1367,1374,1380,1381,1384,1388,1393,1395,1397,1400,1403,1405,1409,1412,1419,1422,1425,1429,1431,1435,1437,1439,1441,1445,1453,1461,1473,1479,1488,1491,1502,1505,1506,1511,1512,1517,1596,1666,1667,1677,1686,1687,1689,1693,1696,1699,1702,1705,1708,1711,1714,1718,1721,1724,1727,1731,1734,1738,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1764,1766,1767,1768,1769,1770,1771,1772,1773,1775,1776,1778,1779,1781,1783,1784,1786,1787,1788,1789,1790,1791,1793,1794,1795,1796,1797,1798,1811,1813,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1829,1830,1831,1832,1833,1834,1835,1837,1841,1845,1861,1862,1863,1864,1865,1869,1870,1871,1872,1874,1876,1878,1880,1882,1883,1884,1885,1887,1889,1891,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1905,1906,1907,1908,1910,1912,1913,1915,1916,1918,1920,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1935,1936,1937,1938,1940,1941,1942,1943,1944,1946,1948,1950,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "200,245,294,458,513,575,639,709,770,845,921,998,1076,1321,1403,1479,1555,1632,1710,1816,1922,2001,2081,2138,2196,2401,2476,2541,2607,2667,2728,2800,2873,2940,3008,3067,3126,3185,3244,3303,3357,3411,3464,3518,3572,3626,3680,3886,3965,4038,4112,4183,4255,4327,4400,4457,4515,4588,4662,4736,4811,4883,4956,5026,5097,5157,5218,5287,5356,5426,5500,5576,5640,5717,5793,5870,5935,6004,6081,6156,6225,6293,6370,6436,6497,6594,6659,6728,6827,6898,6957,7015,7072,7131,7195,7266,7338,7410,7482,7554,7621,7689,7757,7816,7879,7943,8033,8124,8184,8250,8317,8383,8453,8517,8570,8637,8698,8765,8878,8936,8999,9064,9129,9204,9277,9349,9393,9440,9486,9535,9596,9657,9718,9780,9844,9908,9972,10037,10100,10160,10221,10287,10346,10406,10468,10539,10599,10667,11241,11328,11418,11505,11593,11675,11758,11848,11939,12969,13027,13072,13138,13202,13259,13316,13370,15550,15598,15647,15698,16027,16420,16469,16622,17513,17901,17963,18023,18080,18292,18362,18440,18494,18564,18649,18697,18743,18804,18867,18933,18997,19068,19131,19196,19260,19321,19382,19434,19507,19581,19650,19725,19799,19873,20014,20084,26073,28041,28131,28219,28315,28405,28987,29076,29323,29604,29856,30141,30534,31011,31233,31455,31731,31958,32188,32418,32648,32878,33105,33524,33750,34175,34405,34833,35052,35335,35543,35674,35901,36327,36552,36979,37200,37625,37745,38021,38322,38646,38937,39251,39388,39519,39624,39866,40033,40237,40445,40716,40828,40940,41045,41162,41376,41522,41662,41748,42096,42184,42430,42848,43097,43179,43277,43934,44034,44286,44710,44965,45059,45148,45385,47409,47651,47753,48006,50162,60843,62359,73054,74582,76339,76965,77385,78646,79911,80167,80403,80950,81444,82049,82247,82827,84195,84570,84688,85226,85383,85579,85852,86108,86278,86419,86483,86848,87215,87891,88155,88493,88846,88940,89126,89432,89694,89819,89946,90185,90396,90515,90708,90885,91340,91521,91643,91902,92015,92202,92304,92411,92540,92815,93323,93819,94696,94990,95560,95709,96441,96613,96697,97033,97125,97403,103052,108423,108485,109063,109647,109738,109851,110080,110240,110392,110563,110729,110898,111065,111228,111471,111641,111814,111985,112259,112458,112663,112993,113077,113173,113269,113367,113467,113569,113671,113773,113875,113977,114077,114173,114285,114414,114537,114668,114799,114897,115011,115105,115245,115379,115475,115587,115687,115803,115899,116011,116111,116251,116387,116551,116681,116839,116989,117130,117274,117409,117521,117671,117799,117927,118063,118195,118325,118455,118567,118707,119611,119755,119893,119959,120049,120125,120229,120319,120421,120529,120637,120737,120817,120909,121007,121117,121169,121247,121353,121445,121549,121659,121781,121944,122101,122738,122838,122928,123038,123128,123369,123463,123569,123661,123761,123873,123987,124103,124219,124313,124427,124539,124641,124761,124883,124965,125069,125189,125315,125413,125507,125595,125707,125823,125945,126057,126232,126348,126434,126526,126638,126762,126829,126955,127023,127151,127295,127423,127492,127587,127702,127815,127914,128023,128134,128245,128346,128451,128551,128681,128772,128895,128989,129101,129187,129291,129387,129475,129593,129697,129801,129927,130015,130123,130223,130313,130423,130507,130609,130693,130747,130811,130917,131003,131113,131197,131317"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\06a32f20c9dddf4134df46d381c57bac\\transformed\\navigation-runtime-2.5.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "247", "startColumns": "4", "startOffsets": "16258", "endColumns": "52", "endOffsets": "16306"}}]}, {"outputFile": "com.arina.ai.app-mergeDebugResources-64:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\Desktop\\Arina\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "91", "endLines": "14", "endColumns": "12", "endOffsets": "662"}, "to": {"startLines": "1846", "startColumns": "4", "startOffsets": "122106", "endLines": "1857", "endColumns": "12", "endOffsets": "122590"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d9b68a39eaa82fe8f489e3031923adb9\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,236,237,238,240,242,276,325,326,364,365,368,377,378,381,382,386,387,391,392,397,407,408,410,1518,1521,1524", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13588,13647,13706,13766,13826,13886,13946,14006,14066,14126,14186,14246,14306,14365,14425,14485,14545,14605,14665,14725,14785,14845,14905,14965,15024,15084,15144,15203,15262,15321,15380,15439,15703,15777,15835,15947,16032,17789,21137,21202,24007,24073,24332,24920,24972,25150,25212,25490,25526,25790,25840,26078,26612,26659,26749,97408,97520,97631", "endLines": "200,201,202,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,236,237,238,240,242,276,325,326,364,365,368,377,378,381,382,386,387,391,392,397,407,408,410,1520,1523,1527", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "13642,13701,13761,13821,13881,13941,14001,14061,14121,14181,14241,14301,14360,14420,14480,14540,14600,14660,14720,14780,14840,14900,14960,15019,15079,15139,15198,15257,15316,15375,15434,15493,15772,15830,15885,15993,16082,17837,21197,21251,24068,24169,24385,24967,25027,25207,25261,25521,25555,25835,25889,26119,26654,26690,26834,97515,97626,97821"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\c659bea7b1d60839c0336fc95ad688cd\\transformed\\lifecycle-runtime-2.7.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "271", "startColumns": "4", "startOffsets": "17518", "endColumns": "42", "endOffsets": "17556"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e048f096732ed3286ecb33d88bdc8bcb\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "251,272", "startColumns": "4,4", "startOffsets": "16474,17561", "endColumns": "41,59", "endOffsets": "16511,17616"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8d0f68a3cbcc70041c66c54f5b861c50\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "311", "startColumns": "4", "startOffsets": "20135", "endColumns": "82", "endOffsets": "20213"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\aa8b3d3e2faa2b54bbfdbc227ec2daf1\\transformed\\lifecycle-viewmodel-2.7.0\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "274", "startColumns": "4", "startOffsets": "17675", "endColumns": "49", "endOffsets": "17720"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\358e273e9a2417ed24f02c311ea2ead3\\transformed\\fragment-1.5.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "239,252,275", "startColumns": "4,4,4", "startOffsets": "15890,16516,17725", "endColumns": "56,64,63", "endOffsets": "15942,16576,17784"}}, {"source": "C:\\Users\\<USER>\\Desktop\\Arina\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "310,312,324,366,369,370,371,372,373,374,375,379,383,384,385,388,389,390,393,394,398,400,401,402,403,404,409,411,426,427,428,429", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20089,20218,21095,24174,24390,24458,24516,24598,24667,24728,24798,25032,25266,25364,25434,25560,25639,25739,25894,25934,26124,26223,26267,26321,26373,26423,26695,26839,27758,27810,27868,27920", "endColumns": "45,42,41,113,67,57,81,68,60,69,75,48,97,69,55,78,99,50,39,37,45,43,53,51,49,49,53,39,51,57,51,47", "endOffsets": "20130,20256,21132,24283,24453,24511,24593,24662,24723,24793,24869,25076,25359,25429,25485,25634,25734,25785,25929,25967,26165,26262,26316,26368,26418,26468,26744,26874,27805,27863,27915,27963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\be4d7956f63fad6a2908a9518ac09c51\\transformed\\lottie-6.3.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "5,246", "startColumns": "4,4", "startOffsets": "299,16211", "endColumns": "62,46", "endOffsets": "357,16253"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\caaa22807b29cbcd058e6ca17cad94f2\\transformed\\material3-1.1.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "313,314,315,316,327,329,330,331,332,333,336,337,338,339,340,341,342,343,344,345,346,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,367,376,380,395,399,406,412,413,414,415,416,417,418,419,420,421,422,423,424,425", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "20261,20345,20427,20504,21256,21471,21532,21611,21713,21795,21911,21961,22026,22083,22148,22233,22324,22394,22487,22576,22670,22815,22902,22986,23078,23172,23232,23296,23379,23469,23532,23600,23668,23765,23870,23942,24288,24874,25081,25972,26170,26544,26879,26925,26975,27042,27109,27175,27240,27294,27366,27433,27503,27585,27631,27697", "endLines": "313,314,315,316,327,329,330,331,332,335,336,337,338,339,340,341,342,343,344,345,348,349,350,351,352,353,354,355,356,357,358,359,360,361,362,363,367,376,380,395,399,406,412,413,414,415,416,417,418,419,420,421,422,423,424,425", "endColumns": "83,81,76,79,47,60,78,101,81,13,49,64,56,64,84,90,69,92,88,93,13,86,83,91,93,59,63,82,89,62,67,67,96,104,71,64,43,45,68,52,52,67,45,49,66,66,65,64,53,71,66,69,81,45,65,60", "endOffsets": "20340,20422,20499,20579,21299,21527,21606,21708,21790,21906,21956,22021,22078,22143,22228,22319,22389,22482,22571,22665,22810,22897,22981,23073,23167,23227,23291,23374,23464,23527,23595,23663,23760,23865,23937,24002,24327,24915,25145,26020,26218,26607,26920,26970,27037,27104,27170,27235,27289,27361,27428,27498,27580,27626,27692,27753"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\5b15d10bbf71c8d5b5d51f157942f826\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "243,248", "startColumns": "4,4", "startOffsets": "16087,16311", "endColumns": "53,66", "endOffsets": "16136,16373"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e44b6092d301126dd6e126ccf393f4dd\\transformed\\core-1.12.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "6,17,18,31,32,55,56,158,159,160,161,162,163,164,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,197,198,199,244,245,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,282,317,318,319,320,321,322,323,405,1799,1800,1804,1805,1809,1965,1966", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "362,1081,1153,2201,2266,3685,3754,10672,10742,10810,10882,10952,11013,11087,11944,12005,12066,12128,12192,12254,12315,12383,12483,12543,12609,12682,12751,12808,12860,13375,13447,13523,16141,16176,16627,16682,16745,16800,16858,16916,16977,17040,17097,17148,17198,17259,17316,17382,17416,17451,18153,20584,20651,20723,20792,20861,20935,21007,26473,118712,118829,119030,119140,119341,131254,131326", "endLines": "6,17,18,31,32,55,56,158,159,160,161,162,163,164,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,197,198,199,244,245,254,255,256,257,258,259,260,261,262,263,264,265,266,267,268,269,282,317,318,319,320,321,322,323,405,1799,1803,1804,1808,1809,1965,1966", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "417,1148,1236,2261,2327,3749,3812,10737,10805,10877,10947,11008,11082,11155,12000,12061,12123,12187,12249,12310,12378,12478,12538,12604,12677,12746,12803,12855,12917,13442,13518,13583,16171,16206,16677,16740,16795,16853,16911,16972,17035,17092,17143,17193,17254,17311,17377,17411,17446,17481,18218,20646,20718,20787,20856,20930,21002,21090,26539,118824,119025,119135,119336,119465,131321,131388"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\afd914afebd159dc02db3b680f36ef47\\transformed\\play-services-basement-16.0.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "281,328", "startColumns": "4,4", "startOffsets": "18085,21304", "endColumns": "67,166", "endOffsets": "18148,21466"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\222459b6241d56f6f6360aa54805a23e\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "273", "startColumns": "4", "startOffsets": "17621", "endColumns": "53", "endOffsets": "17670"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\895477fa5033432c05b79dc01cd2b72e\\transformed\\appcompat-1.6.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,7,8,9,10,11,12,13,14,15,16,19,20,21,22,23,24,25,26,27,28,29,30,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,165,166,167,168,169,170,171,172,173,189,190,191,192,193,194,195,196,232,233,234,235,241,249,250,253,270,277,278,279,280,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,396,430,431,432,433,434,435,443,444,448,452,456,461,467,474,478,482,487,491,495,499,503,507,511,517,521,527,531,537,541,546,550,553,557,563,567,573,577,583,586,590,594,598,602,606,607,608,609,612,615,618,621,625,626,627,628,629,632,634,636,638,643,644,648,654,658,659,661,673,674,678,684,688,689,690,694,721,725,726,730,758,930,956,1127,1153,1184,1192,1198,1214,1236,1241,1246,1256,1265,1274,1278,1285,1304,1311,1312,1321,1324,1327,1331,1335,1339,1342,1343,1348,1353,1363,1368,1375,1381,1382,1385,1389,1394,1396,1398,1401,1404,1406,1410,1413,1420,1423,1426,1430,1432,1436,1438,1440,1442,1446,1454,1462,1474,1480,1489,1492,1503,1506,1507,1512,1513,1528,1597,1667,1668,1678,1687,1688,1690,1694,1697,1700,1703,1706,1709,1712,1715,1719,1722,1725,1728,1732,1735,1739,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1765,1767,1768,1769,1770,1771,1772,1773,1774,1776,1777,1779,1780,1782,1784,1785,1787,1788,1789,1790,1791,1792,1794,1795,1796,1797,1798,1810,1812,1814,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1828,1830,1831,1832,1833,1834,1835,1836,1838,1842,1858,1859,1860,1861,1862,1863,1867,1868,1869,1870,1872,1874,1876,1878,1880,1881,1882,1883,1885,1887,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1899,1900,1903,1904,1905,1906,1908,1910,1911,1913,1914,1916,1918,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1933,1934,1935,1936,1938,1939,1940,1941,1942,1944,1946,1948,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,250,422,463,518,580,644,714,775,850,926,1003,1241,1326,1408,1484,1560,1637,1715,1821,1927,2006,2086,2143,2332,2406,2481,2546,2612,2672,2733,2805,2878,2945,3013,3072,3131,3190,3249,3308,3362,3416,3469,3523,3577,3631,3817,3891,3970,4043,4117,4188,4260,4332,4405,4462,4520,4593,4667,4741,4816,4888,4961,5031,5102,5162,5223,5292,5361,5431,5505,5581,5645,5722,5798,5875,5940,6009,6086,6161,6230,6298,6375,6441,6502,6599,6664,6733,6832,6903,6962,7020,7077,7136,7200,7271,7343,7415,7487,7559,7626,7694,7762,7821,7884,7948,8038,8129,8189,8255,8322,8388,8458,8522,8575,8642,8703,8770,8883,8941,9004,9069,9134,9209,9282,9354,9398,9445,9491,9540,9601,9662,9723,9785,9849,9913,9977,10042,10105,10165,10226,10292,10351,10411,10473,10544,10604,11160,11246,11333,11423,11510,11598,11680,11763,11853,12922,12974,13032,13077,13143,13207,13264,13321,15498,15555,15603,15652,15998,16378,16425,16581,17486,17842,17906,17968,18028,18223,18297,18367,18445,18499,18569,18654,18702,18748,18809,18872,18938,19002,19073,19136,19201,19265,19326,19387,19439,19512,19586,19655,19730,19804,19878,20019,26025,27968,28046,28136,28224,28320,28410,28992,29081,29328,29609,29861,30146,30539,31016,31238,31460,31736,31963,32193,32423,32653,32883,33110,33529,33755,34180,34410,34838,35057,35340,35548,35679,35906,36332,36557,36984,37205,37630,37750,38026,38327,38651,38942,39256,39393,39524,39629,39871,40038,40242,40450,40721,40833,40945,41050,41167,41381,41527,41667,41753,42101,42189,42435,42853,43102,43184,43282,43939,44039,44291,44715,44970,45064,45153,45390,47414,47656,47758,48011,50167,60848,62364,73059,74587,76344,76970,77390,78651,79916,80172,80408,80955,81449,82054,82252,82832,84200,84575,84693,85231,85388,85584,85857,86113,86283,86424,86488,86853,87220,87896,88160,88498,88851,88945,89131,89437,89699,89824,89951,90190,90401,90520,90713,90890,91345,91526,91648,91907,92020,92207,92309,92416,92545,92820,93328,93824,94701,94995,95565,95714,96446,96618,96702,97038,97130,97826,103057,108428,108490,109068,109652,109743,109856,110085,110245,110397,110568,110734,110903,111070,111233,111476,111646,111819,111990,112264,112463,112668,112998,113082,113178,113274,113372,113472,113574,113676,113778,113880,113982,114082,114178,114290,114419,114542,114673,114804,114902,115016,115110,115250,115384,115480,115592,115692,115808,115904,116016,116116,116256,116392,116556,116686,116844,116994,117135,117279,117414,117526,117676,117804,117932,118068,118200,118330,118460,118572,119470,119616,119760,119898,119964,120054,120130,120234,120324,120426,120534,120642,120742,120822,120914,121012,121122,121174,121252,121358,121450,121554,121664,121786,121949,122595,122675,122775,122865,122975,123065,123306,123400,123506,123598,123698,123810,123924,124040,124156,124250,124364,124476,124578,124698,124820,124902,125006,125126,125252,125350,125444,125532,125644,125760,125882,125994,126169,126285,126371,126463,126575,126699,126766,126892,126960,127088,127232,127360,127429,127524,127639,127752,127851,127960,128071,128182,128283,128388,128488,128618,128709,128832,128926,129038,129124,129228,129324,129412,129530,129634,129738,129864,129952,130060,130160,130250,130360,130444,130546,130630,130684,130748,130854,130940,131050,131134", "endLines": "2,3,4,7,8,9,10,11,12,13,14,15,16,19,20,21,22,23,24,25,26,27,28,29,30,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,165,166,167,168,169,170,171,172,173,189,190,191,192,193,194,195,196,232,233,234,235,241,249,250,253,270,277,278,279,280,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,396,430,431,432,433,434,442,443,447,451,455,460,466,473,477,481,486,490,494,498,502,506,510,516,520,526,530,536,540,545,549,552,556,562,566,572,576,582,585,589,593,597,601,605,606,607,608,611,614,617,620,624,625,626,627,628,631,633,635,637,642,643,647,653,657,658,660,672,673,677,683,687,688,689,693,720,724,725,729,757,929,955,1126,1152,1183,1191,1197,1213,1235,1240,1245,1255,1264,1273,1277,1284,1303,1310,1311,1320,1323,1326,1330,1334,1338,1341,1342,1347,1352,1362,1367,1374,1380,1381,1384,1388,1393,1395,1397,1400,1403,1405,1409,1412,1419,1422,1425,1429,1431,1435,1437,1439,1441,1445,1453,1461,1473,1479,1488,1491,1502,1505,1506,1511,1512,1517,1596,1666,1667,1677,1686,1687,1689,1693,1696,1699,1702,1705,1708,1711,1714,1718,1721,1724,1727,1731,1734,1738,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1764,1766,1767,1768,1769,1770,1771,1772,1773,1775,1776,1778,1779,1781,1783,1784,1786,1787,1788,1789,1790,1791,1793,1794,1795,1796,1797,1798,1811,1813,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1827,1829,1830,1831,1832,1833,1834,1835,1837,1841,1845,1858,1859,1860,1861,1862,1866,1867,1868,1869,1871,1873,1875,1877,1879,1880,1881,1882,1884,1886,1888,1889,1890,1891,1892,1893,1894,1895,1896,1897,1898,1899,1902,1903,1904,1905,1907,1909,1910,1912,1913,1915,1917,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1932,1933,1934,1935,1937,1938,1939,1940,1941,1943,1945,1947,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964", "endColumns": "54,44,48,40,54,61,63,69,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,43,46,45,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,51,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "200,245,294,458,513,575,639,709,770,845,921,998,1076,1321,1403,1479,1555,1632,1710,1816,1922,2001,2081,2138,2196,2401,2476,2541,2607,2667,2728,2800,2873,2940,3008,3067,3126,3185,3244,3303,3357,3411,3464,3518,3572,3626,3680,3886,3965,4038,4112,4183,4255,4327,4400,4457,4515,4588,4662,4736,4811,4883,4956,5026,5097,5157,5218,5287,5356,5426,5500,5576,5640,5717,5793,5870,5935,6004,6081,6156,6225,6293,6370,6436,6497,6594,6659,6728,6827,6898,6957,7015,7072,7131,7195,7266,7338,7410,7482,7554,7621,7689,7757,7816,7879,7943,8033,8124,8184,8250,8317,8383,8453,8517,8570,8637,8698,8765,8878,8936,8999,9064,9129,9204,9277,9349,9393,9440,9486,9535,9596,9657,9718,9780,9844,9908,9972,10037,10100,10160,10221,10287,10346,10406,10468,10539,10599,10667,11241,11328,11418,11505,11593,11675,11758,11848,11939,12969,13027,13072,13138,13202,13259,13316,13370,15550,15598,15647,15698,16027,16420,16469,16622,17513,17901,17963,18023,18080,18292,18362,18440,18494,18564,18649,18697,18743,18804,18867,18933,18997,19068,19131,19196,19260,19321,19382,19434,19507,19581,19650,19725,19799,19873,20014,20084,26073,28041,28131,28219,28315,28405,28987,29076,29323,29604,29856,30141,30534,31011,31233,31455,31731,31958,32188,32418,32648,32878,33105,33524,33750,34175,34405,34833,35052,35335,35543,35674,35901,36327,36552,36979,37200,37625,37745,38021,38322,38646,38937,39251,39388,39519,39624,39866,40033,40237,40445,40716,40828,40940,41045,41162,41376,41522,41662,41748,42096,42184,42430,42848,43097,43179,43277,43934,44034,44286,44710,44965,45059,45148,45385,47409,47651,47753,48006,50162,60843,62359,73054,74582,76339,76965,77385,78646,79911,80167,80403,80950,81444,82049,82247,82827,84195,84570,84688,85226,85383,85579,85852,86108,86278,86419,86483,86848,87215,87891,88155,88493,88846,88940,89126,89432,89694,89819,89946,90185,90396,90515,90708,90885,91340,91521,91643,91902,92015,92202,92304,92411,92540,92815,93323,93819,94696,94990,95560,95709,96441,96613,96697,97033,97125,97403,103052,108423,108485,109063,109647,109738,109851,110080,110240,110392,110563,110729,110898,111065,111228,111471,111641,111814,111985,112259,112458,112663,112993,113077,113173,113269,113367,113467,113569,113671,113773,113875,113977,114077,114173,114285,114414,114537,114668,114799,114897,115011,115105,115245,115379,115475,115587,115687,115803,115899,116011,116111,116251,116387,116551,116681,116839,116989,117130,117274,117409,117521,117671,117799,117927,118063,118195,118325,118455,118567,118707,119611,119755,119893,119959,120049,120125,120229,120319,120421,120529,120637,120737,120817,120909,121007,121117,121169,121247,121353,121445,121549,121659,121781,121944,122101,122670,122770,122860,122970,123060,123301,123395,123501,123593,123693,123805,123919,124035,124151,124245,124359,124471,124573,124693,124815,124897,125001,125121,125247,125345,125439,125527,125639,125755,125877,125989,126164,126280,126366,126458,126570,126694,126761,126887,126955,127083,127227,127355,127424,127519,127634,127747,127846,127955,128066,128177,128278,128383,128483,128613,128704,128827,128921,129033,129119,129223,129319,129407,129525,129629,129733,129859,129947,130055,130155,130245,130355,130439,130541,130625,130679,130743,130849,130935,131045,131129,131249"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\06a32f20c9dddf4134df46d381c57bac\\transformed\\navigation-runtime-2.5.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "247", "startColumns": "4", "startOffsets": "16258", "endColumns": "52", "endOffsets": "16306"}}]}]}