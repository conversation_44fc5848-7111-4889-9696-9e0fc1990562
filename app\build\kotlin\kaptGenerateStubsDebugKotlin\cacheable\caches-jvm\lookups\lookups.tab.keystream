  net    Manifest android  
permission android.Manifest  ANSWER_PHONE_CALLS android.Manifest.permission  
CALL_PHONE android.Manifest.permission  CAMERA android.Manifest.permission  MODIFY_AUDIO_SETTINGS android.Manifest.permission  POST_NOTIFICATIONS android.Manifest.permission  READ_EXTERNAL_STORAGE android.Manifest.permission  READ_PHONE_STATE android.Manifest.permission  RECORD_AUDIO android.Manifest.permission  WRITE_EXTERNAL_STORAGE android.Manifest.permission  Activity android.app  Application android.app  Notification android.app  ActivityResultContracts android.app.Activity  Boolean android.app.Activity  Bundle android.app.Activity  
MainViewModel android.app.Activity  Manifest android.app.Activity  SystemControlService android.app.Activity  TextToSpeech android.app.Activity  VoiceInteractionService android.app.Activity  arrayOf android.app.Activity  
initializeApp android.app.Activity  registerForActivityResult android.app.Activity  
ArinaDatabase android.app.Service  CoroutineScope android.app.Service  Dispatchers android.app.Service  Int android.app.Service  List android.app.Service  MutableStateFlow android.app.Service  NotificationInfo android.app.Service  	StateFlow android.app.Service  StatusBarNotification android.app.Service  String android.app.Service  
SupervisorJob android.app.Service  TextToSpeech android.app.Service  	emptyList android.app.Service  DeviceAdminReceiver android.app.admin  DevicePolicyManager android.app.admin  Context %android.app.admin.DeviceAdminReceiver  Intent %android.app.admin.DeviceAdminReceiver  String %android.app.admin.DeviceAdminReceiver  BluetoothAdapter android.bluetooth  BluetoothManager android.bluetooth  
ComponentName android.content  Context android.content  Intent android.content  Context !android.content.BroadcastReceiver  Intent !android.content.BroadcastReceiver  String !android.content.BroadcastReceiver  ActivityResultContracts android.content.Context  
ArinaDatabase android.content.Context  Boolean android.content.Context  Bundle android.content.Context  CoroutineScope android.content.Context  Dispatchers android.content.Context  Int android.content.Context  List android.content.Context  
MainViewModel android.content.Context  Manifest android.content.Context  MutableStateFlow android.content.Context  NotificationInfo android.content.Context  	StateFlow android.content.Context  StatusBarNotification android.content.Context  String android.content.Context  
SupervisorJob android.content.Context  SystemControlService android.content.Context  TextToSpeech android.content.Context  VoiceInteractionService android.content.Context  arrayOf android.content.Context  	emptyList android.content.Context  
initializeApp android.content.Context  registerForActivityResult android.content.Context  ActivityResultContracts android.content.ContextWrapper  
ArinaDatabase android.content.ContextWrapper  Boolean android.content.ContextWrapper  Bundle android.content.ContextWrapper  CoroutineScope android.content.ContextWrapper  Dispatchers android.content.ContextWrapper  Int android.content.ContextWrapper  List android.content.ContextWrapper  
MainViewModel android.content.ContextWrapper  Manifest android.content.ContextWrapper  MutableStateFlow android.content.ContextWrapper  NotificationInfo android.content.ContextWrapper  	StateFlow android.content.ContextWrapper  StatusBarNotification android.content.ContextWrapper  String android.content.ContextWrapper  
SupervisorJob android.content.ContextWrapper  SystemControlService android.content.ContextWrapper  TextToSpeech android.content.ContextWrapper  VoiceInteractionService android.content.ContextWrapper  arrayOf android.content.ContextWrapper  	emptyList android.content.ContextWrapper  
initializeApp android.content.ContextWrapper  registerForActivityResult android.content.ContextWrapper  PackageManager android.content.pm  AudioFormat 
android.media  AudioManager 
android.media  AudioRecord 
android.media  
MediaRecorder 
android.media  CHANNEL_IN_MONO android.media.AudioFormat  ENCODING_PCM_16BIT android.media.AudioFormat  WifiManager android.net.wifi  Build 
android.os  Bundle 
android.os  PowerManager 
android.os  Settings android.provider  NotificationListenerService android.service.notification  StatusBarNotification android.service.notification  
ArinaDatabase 8android.service.notification.NotificationListenerService  CoroutineScope 8android.service.notification.NotificationListenerService  Dispatchers 8android.service.notification.NotificationListenerService  Int 8android.service.notification.NotificationListenerService  List 8android.service.notification.NotificationListenerService  MutableStateFlow 8android.service.notification.NotificationListenerService  NotificationInfo 8android.service.notification.NotificationListenerService  	StateFlow 8android.service.notification.NotificationListenerService  StatusBarNotification 8android.service.notification.NotificationListenerService  String 8android.service.notification.NotificationListenerService  
SupervisorJob 8android.service.notification.NotificationListenerService  TextToSpeech 8android.service.notification.NotificationListenerService  	emptyList 8android.service.notification.NotificationListenerService  RecognitionListener android.speech  RecognizerIntent android.speech  SpeechRecognizer android.speech  TextToSpeech android.speech.tts  TelecomManager android.telecom  ActivityResultContracts  android.view.ContextThemeWrapper  Boolean  android.view.ContextThemeWrapper  Bundle  android.view.ContextThemeWrapper  
MainViewModel  android.view.ContextThemeWrapper  Manifest  android.view.ContextThemeWrapper  SystemControlService  android.view.ContextThemeWrapper  TextToSpeech  android.view.ContextThemeWrapper  VoiceInteractionService  android.view.ContextThemeWrapper  arrayOf  android.view.ContextThemeWrapper  
initializeApp  android.view.ContextThemeWrapper  registerForActivityResult  android.view.ContextThemeWrapper  Toast android.widget  ComponentActivity androidx.activity  ActivityResultContracts #androidx.activity.ComponentActivity  Boolean #androidx.activity.ComponentActivity  Bundle #androidx.activity.ComponentActivity  
MainViewModel #androidx.activity.ComponentActivity  Manifest #androidx.activity.ComponentActivity  SystemControlService #androidx.activity.ComponentActivity  TextToSpeech #androidx.activity.ComponentActivity  VoiceInteractionService #androidx.activity.ComponentActivity  arrayOf #androidx.activity.ComponentActivity  
initializeApp #androidx.activity.ComponentActivity  registerForActivityResult #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  ActivityResultLauncher androidx.activity.result  <SAM-CONSTRUCTOR> /androidx.activity.result.ActivityResultCallback  ActivityResultContracts !androidx.activity.result.contract  RequestMultiplePermissions 9androidx.activity.result.contract.ActivityResultContracts  invoke ^androidx.activity.result.contract.ActivityResultContracts.RequestMultiplePermissions.Companion  
Composable androidx.compose.animation  ExperimentalMaterial3Api androidx.compose.animation  
Composable androidx.compose.animation.core  RecognitionState androidx.compose.animation.core  Canvas androidx.compose.foundation  
background androidx.compose.foundation  border androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  detectTapGestures $androidx.compose.foundation.gestures  MutableInteractionSource 'androidx.compose.foundation.interaction  collectIsPressedAsState 'androidx.compose.foundation.interaction  Box "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  aspectRatio "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  
LazyColumn  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  GenericShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  Icons androidx.compose.material.icons  Bookmark &androidx.compose.material.icons.filled  BookmarkBorder &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  Mic &androidx.compose.material.icons.filled  MicOff &androidx.compose.material.icons.filled  Settings &androidx.compose.material.icons.filled  ColorScheme androidx.compose.material3  
Composable androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  
MaterialTheme androidx.compose.material3  Shapes androidx.compose.material3  Text androidx.compose.material3  darkColorScheme androidx.compose.material3  ActivityResultContracts androidx.compose.runtime  
Composable androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  Manifest androidx.compose.runtime  RecognitionState androidx.compose.runtime  
SideEffect androidx.compose.runtime  arrayOf androidx.compose.runtime  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  clip androidx.compose.ui.draw  
drawBehind androidx.compose.ui.draw  shadow androidx.compose.ui.draw  Offset androidx.compose.ui.geometry  Size androidx.compose.ui.geometry  Brush androidx.compose.ui.graphics  Color androidx.compose.ui.graphics  
Composable androidx.compose.ui.graphics  Outline androidx.compose.ui.graphics  Path androidx.compose.ui.graphics  Shape androidx.compose.ui.graphics  	StrokeCap androidx.compose.ui.graphics  
graphicsLayer androidx.compose.ui.graphics  toArgb androidx.compose.ui.graphics  Black "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  invoke ,androidx.compose.ui.graphics.Color.Companion  Stroke &androidx.compose.ui.graphics.drawscope  rotate &androidx.compose.ui.graphics.drawscope  pointerInput !androidx.compose.ui.input.pointer  onGloballyPositioned androidx.compose.ui.layout  LocalContext androidx.compose.ui.platform  	LocalView androidx.compose.ui.platform  stringResource androidx.compose.ui.res  	TextStyle androidx.compose.ui.text  invoke ,androidx.compose.ui.text.TextStyle.Companion  Font androidx.compose.ui.text.font  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  invoke ,androidx.compose.ui.text.font.Font.Companion  invoke 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  Light (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  SemiBold (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Light 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  SemiBold 2androidx.compose.ui.text.font.FontWeight.Companion  TextOverflow androidx.compose.ui.text.style  Preview #androidx.compose.ui.tooling.preview  Density androidx.compose.ui.unit  Dp androidx.compose.ui.unit  LayoutDirection androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  sp androidx.compose.ui.unit  ActivityResultContracts #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  
MainViewModel #androidx.core.app.ComponentActivity  Manifest #androidx.core.app.ComponentActivity  SystemControlService #androidx.core.app.ComponentActivity  TextToSpeech #androidx.core.app.ComponentActivity  VoiceInteractionService #androidx.core.app.ComponentActivity  arrayOf #androidx.core.app.ComponentActivity  
initializeApp #androidx.core.app.ComponentActivity  registerForActivityResult #androidx.core.app.ComponentActivity  
ContextCompat androidx.core.content  WindowCompat androidx.core.view  	ViewModel androidx.lifecycle  viewModelScope androidx.lifecycle  A4FResponse androidx.lifecycle.ViewModel  ArinaRepository androidx.lifecycle.ViewModel  Boolean androidx.lifecycle.ViewModel  Conversation androidx.lifecycle.ViewModel  
MainViewEvent androidx.lifecycle.ViewModel  
MainViewState androidx.lifecycle.ViewModel  MutableStateFlow androidx.lifecycle.ViewModel  	StateFlow androidx.lifecycle.ViewModel  String androidx.lifecycle.ViewModel  
SystemCommand androidx.lifecycle.ViewModel  SystemControlService androidx.lifecycle.ViewModel  TextToSpeech androidx.lifecycle.ViewModel  UserPreferences androidx.lifecycle.ViewModel  asStateFlow androidx.lifecycle.ViewModel  com androidx.lifecycle.ViewModel  Dao 
androidx.room  Database 
androidx.room  Delete 
androidx.room  Entity 
androidx.room  Insert 
androidx.room  OnConflictStrategy 
androidx.room  
PrimaryKey 
androidx.room  Query 
androidx.room  Room 
androidx.room  RoomDatabase 
androidx.room  Update 
androidx.room  REPLACE  androidx.room.OnConflictStrategy  REPLACE *androidx.room.OnConflictStrategy.Companion  
ArinaDatabase androidx.room.RoomDatabase  Context androidx.room.RoomDatabase  ConversationDao androidx.room.RoomDatabase  EncryptedSharedPreferences androidx.room.RoomDatabase  String androidx.room.RoomDatabase  UserPreferencesDao androidx.room.RoomDatabase  Volatile androidx.room.RoomDatabase  conversationDao androidx.room.RoomDatabase  userPreferencesDao androidx.room.RoomDatabase  EncryptedSharedPreferences androidx.security.crypto  	MasterKey androidx.security.crypto  
Composable com.airbnb.lottie.compose  ActivityResultContracts com.arina.ai  ArinaApplication com.arina.ai  Boolean com.arina.ai  MainActivity com.arina.ai  Manifest com.arina.ai  R com.arina.ai  arrayOf com.arina.ai  ActivityResultContracts com.arina.ai.MainActivity  Boolean com.arina.ai.MainActivity  Bundle com.arina.ai.MainActivity  
MainViewModel com.arina.ai.MainActivity  Manifest com.arina.ai.MainActivity  SystemControlService com.arina.ai.MainActivity  TextToSpeech com.arina.ai.MainActivity  VoiceInteractionService com.arina.ai.MainActivity  arrayOf com.arina.ai.MainActivity  
getARRAYOf com.arina.ai.MainActivity  
getArrayOf com.arina.ai.MainActivity  
initializeApp com.arina.ai.MainActivity  registerForActivityResult com.arina.ai.MainActivity  font com.arina.ai.R  
A4FRequest com.arina.ai.data.api  A4FResponse com.arina.ai.data.api  
A4FService com.arina.ai.data.api  Float com.arina.ai.data.api  Int com.arina.ai.data.api  List com.arina.ai.data.api  Long com.arina.ai.data.api  String com.arina.ai.data.api  Float  com.arina.ai.data.api.A4FRequest  Int  com.arina.ai.data.api.A4FRequest  List  com.arina.ai.data.api.A4FRequest  String  com.arina.ai.data.api.A4FRequest  Float !com.arina.ai.data.api.A4FResponse  Int !com.arina.ai.data.api.A4FResponse  Long !com.arina.ai.data.api.A4FResponse  String !com.arina.ai.data.api.A4FResponse  
A4FRequest  com.arina.ai.data.api.A4FService  A4FResponse  com.arina.ai.data.api.A4FService  Body  com.arina.ai.data.api.A4FService  Header  com.arina.ai.data.api.A4FService  List  com.arina.ai.data.api.A4FService  POST  com.arina.ai.data.api.A4FService  Response  com.arina.ai.data.api.A4FService  String  com.arina.ai.data.api.A4FService  
ArinaDatabase com.arina.ai.data.database  Boolean com.arina.ai.data.database  Conversation com.arina.ai.data.database  ConversationDao com.arina.ai.data.database  Dao com.arina.ai.data.database  Delete com.arina.ai.data.database  Entity com.arina.ai.data.database  Float com.arina.ai.data.database  Insert com.arina.ai.data.database  Int com.arina.ai.data.database  List com.arina.ai.data.database  Long com.arina.ai.data.database  OnConflictStrategy com.arina.ai.data.database  
PrimaryKey com.arina.ai.data.database  Query com.arina.ai.data.database  String com.arina.ai.data.database  Update com.arina.ai.data.database  UserPreferences com.arina.ai.data.database  UserPreferencesDao com.arina.ai.data.database  Volatile com.arina.ai.data.database  
ArinaDatabase (com.arina.ai.data.database.ArinaDatabase  Context (com.arina.ai.data.database.ArinaDatabase  ConversationDao (com.arina.ai.data.database.ArinaDatabase  EncryptedSharedPreferences (com.arina.ai.data.database.ArinaDatabase  String (com.arina.ai.data.database.ArinaDatabase  UserPreferencesDao (com.arina.ai.data.database.ArinaDatabase  Volatile (com.arina.ai.data.database.ArinaDatabase  conversationDao (com.arina.ai.data.database.ArinaDatabase  userPreferencesDao (com.arina.ai.data.database.ArinaDatabase  
ArinaDatabase 2com.arina.ai.data.database.ArinaDatabase.Companion  Context 2com.arina.ai.data.database.ArinaDatabase.Companion  ConversationDao 2com.arina.ai.data.database.ArinaDatabase.Companion  EncryptedSharedPreferences 2com.arina.ai.data.database.ArinaDatabase.Companion  String 2com.arina.ai.data.database.ArinaDatabase.Companion  UserPreferencesDao 2com.arina.ai.data.database.ArinaDatabase.Companion  Volatile 2com.arina.ai.data.database.ArinaDatabase.Companion  Boolean 'com.arina.ai.data.database.Conversation  Int 'com.arina.ai.data.database.Conversation  Long 'com.arina.ai.data.database.Conversation  
PrimaryKey 'com.arina.ai.data.database.Conversation  String 'com.arina.ai.data.database.Conversation  copy 'com.arina.ai.data.database.Conversation  Conversation *com.arina.ai.data.database.ConversationDao  Delete *com.arina.ai.data.database.ConversationDao  Insert *com.arina.ai.data.database.ConversationDao  List *com.arina.ai.data.database.ConversationDao  Long *com.arina.ai.data.database.ConversationDao  Query *com.arina.ai.data.database.ConversationDao  Update *com.arina.ai.data.database.ConversationDao  deleteConversation *com.arina.ai.data.database.ConversationDao  deleteOldConversations *com.arina.ai.data.database.ConversationDao  updateConversation *com.arina.ai.data.database.ConversationDao  Boolean *com.arina.ai.data.database.UserPreferences  Float *com.arina.ai.data.database.UserPreferences  Int *com.arina.ai.data.database.UserPreferences  
PrimaryKey *com.arina.ai.data.database.UserPreferences  String *com.arina.ai.data.database.UserPreferences  Insert -com.arina.ai.data.database.UserPreferencesDao  OnConflictStrategy -com.arina.ai.data.database.UserPreferencesDao  Query -com.arina.ai.data.database.UserPreferencesDao  Update -com.arina.ai.data.database.UserPreferencesDao  UserPreferences -com.arina.ai.data.database.UserPreferencesDao  updateUserPreferences -com.arina.ai.data.database.UserPreferencesDao  ArinaRepository com.arina.ai.data.repository  Dispatchers com.arina.ai.data.repository  List com.arina.ai.data.repository  Long com.arina.ai.data.repository  Result com.arina.ai.data.repository  String com.arina.ai.data.repository  System com.arina.ai.data.repository  database com.arina.ai.data.repository  withContext com.arina.ai.data.repository  A4FResponse ,com.arina.ai.data.repository.ArinaRepository  
A4FService ,com.arina.ai.data.repository.ArinaRepository  
ArinaDatabase ,com.arina.ai.data.repository.ArinaRepository  Conversation ,com.arina.ai.data.repository.ArinaRepository  Dispatchers ,com.arina.ai.data.repository.ArinaRepository  Flow ,com.arina.ai.data.repository.ArinaRepository  List ,com.arina.ai.data.repository.ArinaRepository  Long ,com.arina.ai.data.repository.ArinaRepository  Result ,com.arina.ai.data.repository.ArinaRepository  String ,com.arina.ai.data.repository.ArinaRepository  System ,com.arina.ai.data.repository.ArinaRepository  UserPreferences ,com.arina.ai.data.repository.ArinaRepository  database ,com.arina.ai.data.repository.ArinaRepository  getWITHContext ,com.arina.ai.data.repository.ArinaRepository  getWithContext ,com.arina.ai.data.repository.ArinaRepository  withContext ,com.arina.ai.data.repository.ArinaRepository  ComponentPreview com.arina.ai.debug  ComponentPreviewPreview com.arina.ai.debug  
Composable com.arina.ai.debug  
DebugActivity com.arina.ai.debug  Bundle  com.arina.ai.debug.DebugActivity  SingletonComponent com.arina.ai.di  SystemModule com.arina.ai.di  ApplicationContext com.arina.ai.di.SystemModule  AudioManager com.arina.ai.di.SystemModule  BluetoothManager com.arina.ai.di.SystemModule  Context com.arina.ai.di.SystemModule  DevicePolicyManager com.arina.ai.di.SystemModule  PowerManager com.arina.ai.di.SystemModule  Provides com.arina.ai.di.SystemModule  	Singleton com.arina.ai.di.SystemModule  TelecomManager com.arina.ai.di.SystemModule  WifiManager com.arina.ai.di.SystemModule  ArinaDeviceAdminReceiver com.arina.ai.receiver  String com.arina.ai.receiver  Context .com.arina.ai.receiver.ArinaDeviceAdminReceiver  Intent .com.arina.ai.receiver.ArinaDeviceAdminReceiver  String .com.arina.ai.receiver.ArinaDeviceAdminReceiver  ArinaNotificationService com.arina.ai.service  AudioFormat com.arina.ai.service  BUFFER_SIZE com.arina.ai.service  Boolean com.arina.ai.service  
ByteBuffer com.arina.ai.service  CoroutineScope com.arina.ai.service  Dispatchers com.arina.ai.service  Float com.arina.ai.service  Int com.arina.ai.service  List com.arina.ai.service  Long com.arina.ai.service  MutableStateFlow com.arina.ai.service  NotificationInfo com.arina.ai.service  RecognitionState com.arina.ai.service  
ShortArray com.arina.ai.service  SpeechRecognizer com.arina.ai.service  String com.arina.ai.service  
SupervisorJob com.arina.ai.service  SystemControlService com.arina.ai.service  Unit com.arina.ai.service  VoiceInteractionService com.arina.ai.service  
VoiceState com.arina.ai.service  WakeWordDetector com.arina.ai.service  	emptyList com.arina.ai.service  getValue com.arina.ai.service  invoke com.arina.ai.service  lazy com.arina.ai.service  provideDelegate com.arina.ai.service  
ArinaDatabase -com.arina.ai.service.ArinaNotificationService  CoroutineScope -com.arina.ai.service.ArinaNotificationService  Dispatchers -com.arina.ai.service.ArinaNotificationService  Int -com.arina.ai.service.ArinaNotificationService  List -com.arina.ai.service.ArinaNotificationService  MutableStateFlow -com.arina.ai.service.ArinaNotificationService  NotificationInfo -com.arina.ai.service.ArinaNotificationService  	StateFlow -com.arina.ai.service.ArinaNotificationService  StatusBarNotification -com.arina.ai.service.ArinaNotificationService  String -com.arina.ai.service.ArinaNotificationService  
SupervisorJob -com.arina.ai.service.ArinaNotificationService  TextToSpeech -com.arina.ai.service.ArinaNotificationService  _notifications -com.arina.ai.service.ArinaNotificationService  	emptyList -com.arina.ai.service.ArinaNotificationService  getEMPTYList -com.arina.ai.service.ArinaNotificationService  getEmptyList -com.arina.ai.service.ArinaNotificationService  Boolean %com.arina.ai.service.NotificationInfo  Int %com.arina.ai.service.NotificationInfo  Long %com.arina.ai.service.NotificationInfo  String %com.arina.ai.service.NotificationInfo  Float %com.arina.ai.service.RecognitionState  Idle %com.arina.ai.service.RecognitionState  RecognitionState %com.arina.ai.service.RecognitionState  String %com.arina.ai.service.RecognitionState  String +com.arina.ai.service.RecognitionState.Error  Float 8com.arina.ai.service.RecognitionState.ListeningWithLevel  String 3com.arina.ai.service.RecognitionState.PartialResult  String -com.arina.ai.service.RecognitionState.Success  ApplicationContext %com.arina.ai.service.SpeechRecognizer  Context %com.arina.ai.service.SpeechRecognizer  Inject %com.arina.ai.service.SpeechRecognizer  MutableStateFlow %com.arina.ai.service.SpeechRecognizer  RecognitionState %com.arina.ai.service.SpeechRecognizer  SpeechRecognizer %com.arina.ai.service.SpeechRecognizer  	StateFlow %com.arina.ai.service.SpeechRecognizer  String %com.arina.ai.service.SpeechRecognizer  Unit %com.arina.ai.service.SpeechRecognizer  _recognitionState %com.arina.ai.service.SpeechRecognizer  ApplicationContext )com.arina.ai.service.SystemControlService  AudioManager )com.arina.ai.service.SystemControlService  BluetoothManager )com.arina.ai.service.SystemControlService  Boolean )com.arina.ai.service.SystemControlService  Context )com.arina.ai.service.SystemControlService  DevicePolicyManager )com.arina.ai.service.SystemControlService  Inject )com.arina.ai.service.SystemControlService  Int )com.arina.ai.service.SystemControlService  PowerManager )com.arina.ai.service.SystemControlService  String )com.arina.ai.service.SystemControlService  TelecomManager )com.arina.ai.service.SystemControlService  WifiManager )com.arina.ai.service.SystemControlService  Context ,com.arina.ai.service.VoiceInteractionService  CoroutineScope ,com.arina.ai.service.VoiceInteractionService  Dispatchers ,com.arina.ai.service.VoiceInteractionService  MutableStateFlow ,com.arina.ai.service.VoiceInteractionService  SpeechRecognizer ,com.arina.ai.service.VoiceInteractionService  	StateFlow ,com.arina.ai.service.VoiceInteractionService  String ,com.arina.ai.service.VoiceInteractionService  
SupervisorJob ,com.arina.ai.service.VoiceInteractionService  Unit ,com.arina.ai.service.VoiceInteractionService  
VoiceState ,com.arina.ai.service.VoiceInteractionService  WakeWordDetector ,com.arina.ai.service.VoiceInteractionService  _voiceState ,com.arina.ai.service.VoiceInteractionService  context ,com.arina.ai.service.VoiceInteractionService  getGETValue ,com.arina.ai.service.VoiceInteractionService  getGetValue ,com.arina.ai.service.VoiceInteractionService  getLAZY ,com.arina.ai.service.VoiceInteractionService  getLazy ,com.arina.ai.service.VoiceInteractionService  getPROVIDEDelegate ,com.arina.ai.service.VoiceInteractionService  getProvideDelegate ,com.arina.ai.service.VoiceInteractionService  getValue ,com.arina.ai.service.VoiceInteractionService  invoke ,com.arina.ai.service.VoiceInteractionService  lazy ,com.arina.ai.service.VoiceInteractionService  provideDelegate ,com.arina.ai.service.VoiceInteractionService  Idle com.arina.ai.service.VoiceState  String com.arina.ai.service.VoiceState  
VoiceState com.arina.ai.service.VoiceState  String %com.arina.ai.service.VoiceState.Error  ApplicationContext %com.arina.ai.service.WakeWordDetector  AudioFormat %com.arina.ai.service.WakeWordDetector  AudioRecord %com.arina.ai.service.WakeWordDetector  BUFFER_SIZE %com.arina.ai.service.WakeWordDetector  Boolean %com.arina.ai.service.WakeWordDetector  
ByteBuffer %com.arina.ai.service.WakeWordDetector  Context %com.arina.ai.service.WakeWordDetector  Inject %com.arina.ai.service.WakeWordDetector  Int %com.arina.ai.service.WakeWordDetector  Interpreter %com.arina.ai.service.WakeWordDetector  Job %com.arina.ai.service.WakeWordDetector  
ShortArray %com.arina.ai.service.WakeWordDetector  Unit %com.arina.ai.service.WakeWordDetector  ApplicationContext /com.arina.ai.service.WakeWordDetector.Companion  AudioFormat /com.arina.ai.service.WakeWordDetector.Companion  AudioRecord /com.arina.ai.service.WakeWordDetector.Companion  BUFFER_SIZE /com.arina.ai.service.WakeWordDetector.Companion  Boolean /com.arina.ai.service.WakeWordDetector.Companion  
ByteBuffer /com.arina.ai.service.WakeWordDetector.Companion  Context /com.arina.ai.service.WakeWordDetector.Companion  Inject /com.arina.ai.service.WakeWordDetector.Companion  Int /com.arina.ai.service.WakeWordDetector.Companion  Interpreter /com.arina.ai.service.WakeWordDetector.Companion  Job /com.arina.ai.service.WakeWordDetector.Companion  SAMPLE_RATE /com.arina.ai.service.WakeWordDetector.Companion  
ShortArray /com.arina.ai.service.WakeWordDetector.Companion  Unit /com.arina.ai.service.WakeWordDetector.Companion  invoke /com.arina.ai.service.WakeWordDetector.Companion  ArinaButton com.arina.ai.ui.components  Boolean com.arina.ai.ui.components  
Composable com.arina.ai.ui.components  ConversationItem com.arina.ai.ui.components  ConversationList com.arina.ai.ui.components  EmotionalState com.arina.ai.ui.components  ExperimentalMaterial3Api com.arina.ai.ui.components  Float com.arina.ai.ui.components  HolographicCard com.arina.ai.ui.components  Int com.arina.ai.ui.components  List com.arina.ai.ui.components  LottiePersonalityAvatar com.arina.ai.ui.components  OptIn com.arina.ai.ui.components  PersonalityAvatar com.arina.ai.ui.components  RecognitionState com.arina.ai.ui.components  SettingsDialog com.arina.ai.ui.components  String com.arina.ai.ui.components  Unit com.arina.ai.ui.components  VoiceWaveAnimation com.arina.ai.ui.components  
VoiceWaveform com.arina.ai.ui.components  VoiceWaveformWithState com.arina.ai.ui.components  
Composable com.arina.ai.ui.screens  ExperimentalMaterial3Api com.arina.ai.ui.screens  
MainScreen com.arina.ai.ui.screens  OptIn com.arina.ai.ui.screens  SettingsScreen com.arina.ai.ui.screens  Unit com.arina.ai.ui.screens  ArinaColors com.arina.ai.ui.theme  ArinaShapes com.arina.ai.ui.theme  
ArinaTheme com.arina.ai.ui.theme  ArinaThemeUtils com.arina.ai.ui.theme  ArinaTypography com.arina.ai.ui.theme  Boolean com.arina.ai.ui.theme  Color com.arina.ai.ui.theme  CyberButton com.arina.ai.ui.theme  	CyberCard com.arina.ai.ui.theme  CyberDialog com.arina.ai.ui.theme  
CyberShape com.arina.ai.ui.theme  DarkColorScheme com.arina.ai.ui.theme  DiagonalCard com.arina.ai.ui.theme  DiagonalCutShape com.arina.ai.ui.theme  Float com.arina.ai.ui.theme  
FontWeight com.arina.ai.ui.theme  
HexagonAvatar com.arina.ai.ui.theme  HexagonShape com.arina.ai.ui.theme  LightColorScheme com.arina.ai.ui.theme  Poppins com.arina.ai.ui.theme  R com.arina.ai.ui.theme  	TextStyle com.arina.ai.ui.theme  Unit com.arina.ai.ui.theme  darkColorScheme com.arina.ai.ui.theme  
BorderPrimary !com.arina.ai.ui.theme.ArinaColors  BorderSecondary !com.arina.ai.ui.theme.ArinaColors  Color !com.arina.ai.ui.theme.ArinaColors  CyanGlow !com.arina.ai.ui.theme.ArinaColors  	DeepBlack !com.arina.ai.ui.theme.ArinaColors  Error !com.arina.ai.ui.theme.ArinaColors  MagentaGlow !com.arina.ai.ui.theme.ArinaColors  NeonCyan !com.arina.ai.ui.theme.ArinaColors  NeonMagenta !com.arina.ai.ui.theme.ArinaColors  
NeonPurple !com.arina.ai.ui.theme.ArinaColors  SurfacePrimary !com.arina.ai.ui.theme.ArinaColors  SurfaceSecondary !com.arina.ai.ui.theme.ArinaColors  TextPrimary !com.arina.ai.ui.theme.ArinaColors  
TextSecondary !com.arina.ai.ui.theme.ArinaColors  
VioletGlow !com.arina.ai.ui.theme.ArinaColors  invoke !com.arina.ai.ui.theme.ArinaColors  Color %com.arina.ai.ui.theme.ArinaThemeUtils  Float %com.arina.ai.ui.theme.ArinaThemeUtils  
FontWeight %com.arina.ai.ui.theme.ArinaTypography  Poppins %com.arina.ai.ui.theme.ArinaTypography  	TextStyle %com.arina.ai.ui.theme.ArinaTypography  invoke %com.arina.ai.ui.theme.ArinaTypography  sp %com.arina.ai.ui.theme.ArinaTypography  Density  com.arina.ai.ui.theme.CyberShape  Float  com.arina.ai.ui.theme.CyberShape  LayoutDirection  com.arina.ai.ui.theme.CyberShape  Outline  com.arina.ai.ui.theme.CyberShape  Size  com.arina.ai.ui.theme.CyberShape  Density &com.arina.ai.ui.theme.DiagonalCutShape  Float &com.arina.ai.ui.theme.DiagonalCutShape  LayoutDirection &com.arina.ai.ui.theme.DiagonalCutShape  Outline &com.arina.ai.ui.theme.DiagonalCutShape  Size &com.arina.ai.ui.theme.DiagonalCutShape  Density "com.arina.ai.ui.theme.HexagonShape  LayoutDirection "com.arina.ai.ui.theme.HexagonShape  Outline "com.arina.ai.ui.theme.HexagonShape  Size "com.arina.ai.ui.theme.HexagonShape  Any com.arina.ai.viewmodel  Boolean com.arina.ai.viewmodel  Int com.arina.ai.viewmodel  List com.arina.ai.viewmodel  
MainViewEvent com.arina.ai.viewmodel  
MainViewModel com.arina.ai.viewmodel  
MainViewState com.arina.ai.viewmodel  Map com.arina.ai.viewmodel  MutableStateFlow com.arina.ai.viewmodel  	StateFlow com.arina.ai.viewmodel  String com.arina.ai.viewmodel  
SystemCommand com.arina.ai.viewmodel  asStateFlow com.arina.ai.viewmodel  com com.arina.ai.viewmodel  Boolean $com.arina.ai.viewmodel.MainViewEvent  Conversation $com.arina.ai.viewmodel.MainViewEvent  
MainViewEvent $com.arina.ai.viewmodel.MainViewEvent  String $com.arina.ai.viewmodel.MainViewEvent  
SystemCommand $com.arina.ai.viewmodel.MainViewEvent  UserPreferences $com.arina.ai.viewmodel.MainViewEvent  Conversation 9com.arina.ai.viewmodel.MainViewEvent.BookmarkConversation  Conversation 7com.arina.ai.viewmodel.MainViewEvent.DeleteConversation  
SystemCommand 9com.arina.ai.viewmodel.MainViewEvent.ExecuteSystemCommand  String 5com.arina.ai.viewmodel.MainViewEvent.ProcessTextInput  String 6com.arina.ai.viewmodel.MainViewEvent.ProcessVoiceInput  Boolean 3com.arina.ai.viewmodel.MainViewEvent.StartListening  UserPreferences 6com.arina.ai.viewmodel.MainViewEvent.UpdatePreferences  A4FResponse $com.arina.ai.viewmodel.MainViewModel  ArinaRepository $com.arina.ai.viewmodel.MainViewModel  Boolean $com.arina.ai.viewmodel.MainViewModel  Conversation $com.arina.ai.viewmodel.MainViewModel  
MainViewEvent $com.arina.ai.viewmodel.MainViewModel  
MainViewState $com.arina.ai.viewmodel.MainViewModel  MutableStateFlow $com.arina.ai.viewmodel.MainViewModel  	StateFlow $com.arina.ai.viewmodel.MainViewModel  String $com.arina.ai.viewmodel.MainViewModel  
SystemCommand $com.arina.ai.viewmodel.MainViewModel  SystemControlService $com.arina.ai.viewmodel.MainViewModel  TextToSpeech $com.arina.ai.viewmodel.MainViewModel  UserPreferences $com.arina.ai.viewmodel.MainViewModel  
_viewState $com.arina.ai.viewmodel.MainViewModel  asStateFlow $com.arina.ai.viewmodel.MainViewModel  com $com.arina.ai.viewmodel.MainViewModel  getASStateFlow $com.arina.ai.viewmodel.MainViewModel  getAsStateFlow $com.arina.ai.viewmodel.MainViewModel  Any $com.arina.ai.viewmodel.MainViewState  Boolean $com.arina.ai.viewmodel.MainViewState  Conversation $com.arina.ai.viewmodel.MainViewState  EmotionalState $com.arina.ai.viewmodel.MainViewState  List $com.arina.ai.viewmodel.MainViewState  Map $com.arina.ai.viewmodel.MainViewState  String $com.arina.ai.viewmodel.MainViewState  UserPreferences $com.arina.ai.viewmodel.MainViewState  Boolean $com.arina.ai.viewmodel.SystemCommand  Int $com.arina.ai.viewmodel.SystemCommand  String $com.arina.ai.viewmodel.SystemCommand  
SystemCommand $com.arina.ai.viewmodel.SystemCommand  String -com.arina.ai.viewmodel.SystemCommand.CloseApp  String .com.arina.ai.viewmodel.SystemCommand.LaunchApp  String -com.arina.ai.viewmodel.SystemCommand.MakeCall  String 1com.arina.ai.viewmodel.SystemCommand.OpenSettings  Boolean 1com.arina.ai.viewmodel.SystemCommand.SetBluetooth  Int 2com.arina.ai.viewmodel.SystemCommand.SetBrightness  Boolean ,com.arina.ai.viewmodel.SystemCommand.SetMute  Int ,com.arina.ai.viewmodel.SystemCommand.SetMute  Int .com.arina.ai.viewmodel.SystemCommand.SetVolume  Boolean ,com.arina.ai.viewmodel.SystemCommand.SetWifi  Module dagger  Provides dagger  	InstallIn dagger.hilt  AndroidEntryPoint dagger.hilt.android  HiltAndroidApp dagger.hilt.android  ApplicationContext dagger.hilt.android.qualifiers  SingletonComponent dagger.hilt.components  File java.io  ActivityResultContracts 	java.lang  AudioFormat 	java.lang  BUFFER_SIZE 	java.lang  
ByteBuffer 	java.lang  Color 	java.lang  Conversation 	java.lang  CoroutineScope 	java.lang  Dispatchers 	java.lang  ExperimentalMaterial3Api 	java.lang  
FontWeight 	java.lang  
MainViewState 	java.lang  Manifest 	java.lang  MutableStateFlow 	java.lang  OnConflictStrategy 	java.lang  Poppins 	java.lang  R 	java.lang  RecognitionState 	java.lang  SingletonComponent 	java.lang  SpeechRecognizer 	java.lang  
SupervisorJob 	java.lang  System 	java.lang  	TextStyle 	java.lang  UserPreferences 	java.lang  
VoiceState 	java.lang  WakeWordDetector 	java.lang  arrayOf 	java.lang  asStateFlow 	java.lang  com 	java.lang  database 	java.lang  	emptyList 	java.lang  getValue 	java.lang  lazy 	java.lang  provideDelegate 	java.lang  withContext 	java.lang  currentTimeMillis java.lang.System  
ByteBuffer java.nio  	ByteOrder java.nio  allocateDirect java.nio.ByteBuffer  SimpleDateFormat 	java.text  ActivityResultContracts 	java.util  
Composable 	java.util  CoroutineScope 	java.util  Dao 	java.util  Delete 	java.util  Dispatchers 	java.util  Entity 	java.util  Insert 	java.util  
MainViewState 	java.util  Manifest 	java.util  MutableStateFlow 	java.util  OnConflictStrategy 	java.util  
PrimaryKey 	java.util  Query 	java.util  RecognitionState 	java.util  Result 	java.util  	StateFlow 	java.util  System 	java.util  Update 	java.util  arrayOf 	java.util  asStateFlow 	java.util  com 	java.util  database 	java.util  withContext 	java.util  Inject javax.inject  	Singleton javax.inject  ActivityResultContracts kotlin  Any kotlin  Array kotlin  AudioFormat kotlin  BUFFER_SIZE kotlin  Boolean kotlin  
ByteBuffer kotlin  Color kotlin  Conversation kotlin  CoroutineScope kotlin  Dispatchers kotlin  Double kotlin  ExperimentalMaterial3Api kotlin  Float kotlin  
FontWeight kotlin  	Function0 kotlin  	Function1 kotlin  Int kotlin  Lazy kotlin  Long kotlin  
MainViewState kotlin  Manifest kotlin  MutableStateFlow kotlin  Nothing kotlin  OnConflictStrategy kotlin  OptIn kotlin  Poppins kotlin  R kotlin  RecognitionState kotlin  Result kotlin  
ShortArray kotlin  SingletonComponent kotlin  SpeechRecognizer kotlin  String kotlin  
SupervisorJob kotlin  System kotlin  	TextStyle kotlin  Unit kotlin  UserPreferences kotlin  
VoiceState kotlin  Volatile kotlin  WakeWordDetector kotlin  arrayOf kotlin  asStateFlow kotlin  com kotlin  database kotlin  	emptyList kotlin  getValue kotlin  lazy kotlin  provideDelegate kotlin  withContext kotlin  getSP 
kotlin.Double  getSp 
kotlin.Double  getDP 
kotlin.Int  getDp 
kotlin.Int  getSP 
kotlin.Int  getSp 
kotlin.Int  getGETValue kotlin.Lazy  getGetValue kotlin.Lazy  getPROVIDEDelegate kotlin.Lazy  getProvideDelegate kotlin.Lazy  getValue kotlin.Lazy  provideDelegate kotlin.Lazy  ActivityResultContracts kotlin.annotation  AudioFormat kotlin.annotation  BUFFER_SIZE kotlin.annotation  
ByteBuffer kotlin.annotation  Color kotlin.annotation  Conversation kotlin.annotation  CoroutineScope kotlin.annotation  Dispatchers kotlin.annotation  ExperimentalMaterial3Api kotlin.annotation  
FontWeight kotlin.annotation  
MainViewState kotlin.annotation  Manifest kotlin.annotation  MutableStateFlow kotlin.annotation  OnConflictStrategy kotlin.annotation  Poppins kotlin.annotation  R kotlin.annotation  RecognitionState kotlin.annotation  Result kotlin.annotation  SingletonComponent kotlin.annotation  SpeechRecognizer kotlin.annotation  
SupervisorJob kotlin.annotation  System kotlin.annotation  	TextStyle kotlin.annotation  UserPreferences kotlin.annotation  
VoiceState kotlin.annotation  Volatile kotlin.annotation  WakeWordDetector kotlin.annotation  arrayOf kotlin.annotation  asStateFlow kotlin.annotation  com kotlin.annotation  database kotlin.annotation  	emptyList kotlin.annotation  getValue kotlin.annotation  lazy kotlin.annotation  provideDelegate kotlin.annotation  withContext kotlin.annotation  ActivityResultContracts kotlin.collections  AudioFormat kotlin.collections  BUFFER_SIZE kotlin.collections  
ByteBuffer kotlin.collections  Color kotlin.collections  Conversation kotlin.collections  CoroutineScope kotlin.collections  Dispatchers kotlin.collections  ExperimentalMaterial3Api kotlin.collections  
FontWeight kotlin.collections  List kotlin.collections  
MainViewState kotlin.collections  Manifest kotlin.collections  Map kotlin.collections  MutableStateFlow kotlin.collections  OnConflictStrategy kotlin.collections  Poppins kotlin.collections  R kotlin.collections  RecognitionState kotlin.collections  Result kotlin.collections  SingletonComponent kotlin.collections  SpeechRecognizer kotlin.collections  
SupervisorJob kotlin.collections  System kotlin.collections  	TextStyle kotlin.collections  UserPreferences kotlin.collections  
VoiceState kotlin.collections  Volatile kotlin.collections  WakeWordDetector kotlin.collections  arrayOf kotlin.collections  asStateFlow kotlin.collections  com kotlin.collections  database kotlin.collections  	emptyList kotlin.collections  getValue kotlin.collections  lazy kotlin.collections  provideDelegate kotlin.collections  withContext kotlin.collections  ActivityResultContracts kotlin.comparisons  AudioFormat kotlin.comparisons  BUFFER_SIZE kotlin.comparisons  
ByteBuffer kotlin.comparisons  Color kotlin.comparisons  Conversation kotlin.comparisons  CoroutineScope kotlin.comparisons  Dispatchers kotlin.comparisons  ExperimentalMaterial3Api kotlin.comparisons  
FontWeight kotlin.comparisons  
MainViewState kotlin.comparisons  Manifest kotlin.comparisons  MutableStateFlow kotlin.comparisons  OnConflictStrategy kotlin.comparisons  Poppins kotlin.comparisons  R kotlin.comparisons  RecognitionState kotlin.comparisons  Result kotlin.comparisons  SingletonComponent kotlin.comparisons  SpeechRecognizer kotlin.comparisons  
SupervisorJob kotlin.comparisons  System kotlin.comparisons  	TextStyle kotlin.comparisons  UserPreferences kotlin.comparisons  
VoiceState kotlin.comparisons  Volatile kotlin.comparisons  WakeWordDetector kotlin.comparisons  arrayOf kotlin.comparisons  asStateFlow kotlin.comparisons  com kotlin.comparisons  database kotlin.comparisons  	emptyList kotlin.comparisons  getValue kotlin.comparisons  lazy kotlin.comparisons  provideDelegate kotlin.comparisons  withContext kotlin.comparisons  CoroutineContext kotlin.coroutines  SuspendFunction1 kotlin.coroutines  ActivityResultContracts 	kotlin.io  AudioFormat 	kotlin.io  BUFFER_SIZE 	kotlin.io  
ByteBuffer 	kotlin.io  Color 	kotlin.io  Conversation 	kotlin.io  CoroutineScope 	kotlin.io  Dispatchers 	kotlin.io  ExperimentalMaterial3Api 	kotlin.io  
FontWeight 	kotlin.io  
MainViewState 	kotlin.io  Manifest 	kotlin.io  MutableStateFlow 	kotlin.io  OnConflictStrategy 	kotlin.io  Poppins 	kotlin.io  R 	kotlin.io  RecognitionState 	kotlin.io  Result 	kotlin.io  SingletonComponent 	kotlin.io  SpeechRecognizer 	kotlin.io  
SupervisorJob 	kotlin.io  System 	kotlin.io  	TextStyle 	kotlin.io  UserPreferences 	kotlin.io  
VoiceState 	kotlin.io  Volatile 	kotlin.io  WakeWordDetector 	kotlin.io  arrayOf 	kotlin.io  asStateFlow 	kotlin.io  com 	kotlin.io  database 	kotlin.io  	emptyList 	kotlin.io  getValue 	kotlin.io  lazy 	kotlin.io  provideDelegate 	kotlin.io  withContext 	kotlin.io  ActivityResultContracts 
kotlin.jvm  AudioFormat 
kotlin.jvm  BUFFER_SIZE 
kotlin.jvm  
ByteBuffer 
kotlin.jvm  Color 
kotlin.jvm  Conversation 
kotlin.jvm  CoroutineScope 
kotlin.jvm  Dispatchers 
kotlin.jvm  ExperimentalMaterial3Api 
kotlin.jvm  
FontWeight 
kotlin.jvm  
MainViewState 
kotlin.jvm  Manifest 
kotlin.jvm  MutableStateFlow 
kotlin.jvm  OnConflictStrategy 
kotlin.jvm  Poppins 
kotlin.jvm  R 
kotlin.jvm  RecognitionState 
kotlin.jvm  Result 
kotlin.jvm  SingletonComponent 
kotlin.jvm  SpeechRecognizer 
kotlin.jvm  
SupervisorJob 
kotlin.jvm  System 
kotlin.jvm  	TextStyle 
kotlin.jvm  UserPreferences 
kotlin.jvm  
VoiceState 
kotlin.jvm  Volatile 
kotlin.jvm  WakeWordDetector 
kotlin.jvm  arrayOf 
kotlin.jvm  asStateFlow 
kotlin.jvm  com 
kotlin.jvm  database 
kotlin.jvm  	emptyList 
kotlin.jvm  getValue 
kotlin.jvm  lazy 
kotlin.jvm  provideDelegate 
kotlin.jvm  withContext 
kotlin.jvm  
Composable kotlin.math  PI kotlin.math  cos kotlin.math  min kotlin.math  sin kotlin.math  ActivityResultContracts 
kotlin.ranges  AudioFormat 
kotlin.ranges  BUFFER_SIZE 
kotlin.ranges  
ByteBuffer 
kotlin.ranges  Color 
kotlin.ranges  Conversation 
kotlin.ranges  CoroutineScope 
kotlin.ranges  Dispatchers 
kotlin.ranges  ExperimentalMaterial3Api 
kotlin.ranges  
FontWeight 
kotlin.ranges  
MainViewState 
kotlin.ranges  Manifest 
kotlin.ranges  MutableStateFlow 
kotlin.ranges  OnConflictStrategy 
kotlin.ranges  Poppins 
kotlin.ranges  R 
kotlin.ranges  RecognitionState 
kotlin.ranges  Result 
kotlin.ranges  SingletonComponent 
kotlin.ranges  SpeechRecognizer 
kotlin.ranges  
SupervisorJob 
kotlin.ranges  System 
kotlin.ranges  	TextStyle 
kotlin.ranges  UserPreferences 
kotlin.ranges  
VoiceState 
kotlin.ranges  Volatile 
kotlin.ranges  WakeWordDetector 
kotlin.ranges  arrayOf 
kotlin.ranges  asStateFlow 
kotlin.ranges  com 
kotlin.ranges  database 
kotlin.ranges  	emptyList 
kotlin.ranges  getValue 
kotlin.ranges  lazy 
kotlin.ranges  provideDelegate 
kotlin.ranges  withContext 
kotlin.ranges  KClass kotlin.reflect  ActivityResultContracts kotlin.sequences  AudioFormat kotlin.sequences  BUFFER_SIZE kotlin.sequences  
ByteBuffer kotlin.sequences  Color kotlin.sequences  Conversation kotlin.sequences  CoroutineScope kotlin.sequences  Dispatchers kotlin.sequences  ExperimentalMaterial3Api kotlin.sequences  
FontWeight kotlin.sequences  
MainViewState kotlin.sequences  Manifest kotlin.sequences  MutableStateFlow kotlin.sequences  OnConflictStrategy kotlin.sequences  Poppins kotlin.sequences  R kotlin.sequences  RecognitionState kotlin.sequences  Result kotlin.sequences  SingletonComponent kotlin.sequences  SpeechRecognizer kotlin.sequences  
SupervisorJob kotlin.sequences  System kotlin.sequences  	TextStyle kotlin.sequences  UserPreferences kotlin.sequences  
VoiceState kotlin.sequences  Volatile kotlin.sequences  WakeWordDetector kotlin.sequences  arrayOf kotlin.sequences  asStateFlow kotlin.sequences  com kotlin.sequences  database kotlin.sequences  	emptyList kotlin.sequences  getValue kotlin.sequences  lazy kotlin.sequences  provideDelegate kotlin.sequences  withContext kotlin.sequences  ActivityResultContracts kotlin.text  AudioFormat kotlin.text  BUFFER_SIZE kotlin.text  
ByteBuffer kotlin.text  Color kotlin.text  Conversation kotlin.text  CoroutineScope kotlin.text  Dispatchers kotlin.text  ExperimentalMaterial3Api kotlin.text  
FontWeight kotlin.text  
MainViewState kotlin.text  Manifest kotlin.text  MutableStateFlow kotlin.text  OnConflictStrategy kotlin.text  Poppins kotlin.text  R kotlin.text  RecognitionState kotlin.text  Result kotlin.text  SingletonComponent kotlin.text  SpeechRecognizer kotlin.text  
SupervisorJob kotlin.text  System kotlin.text  	TextStyle kotlin.text  UserPreferences kotlin.text  
VoiceState kotlin.text  Volatile kotlin.text  WakeWordDetector kotlin.text  arrayOf kotlin.text  asStateFlow kotlin.text  com kotlin.text  database kotlin.text  	emptyList kotlin.text  getValue kotlin.text  lazy kotlin.text  provideDelegate kotlin.text  withContext kotlin.text  CompletableJob kotlinx.coroutines  CoroutineDispatcher kotlinx.coroutines  CoroutineScope kotlinx.coroutines  Dispatchers kotlinx.coroutines  Job kotlinx.coroutines  
SupervisorJob kotlinx.coroutines  launch kotlinx.coroutines  withContext kotlinx.coroutines  plus !kotlinx.coroutines.CompletableJob  System !kotlinx.coroutines.CoroutineScope  database !kotlinx.coroutines.CoroutineScope  getDATABASE !kotlinx.coroutines.CoroutineScope  getDatabase !kotlinx.coroutines.CoroutineScope  Default kotlinx.coroutines.Dispatchers  IO kotlinx.coroutines.Dispatchers  Flow kotlinx.coroutines.flow  
MainViewState kotlinx.coroutines.flow  MutableStateFlow kotlinx.coroutines.flow  	StateFlow kotlinx.coroutines.flow  asStateFlow kotlinx.coroutines.flow  com kotlinx.coroutines.flow  flow kotlinx.coroutines.flow  flowOn kotlinx.coroutines.flow  asStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getASStateFlow (kotlinx.coroutines.flow.MutableStateFlow  getAsStateFlow (kotlinx.coroutines.flow.MutableStateFlow  Interpreter org.tensorflow.lite  Response 	retrofit2  Retrofit 	retrofit2  GsonConverterFactory retrofit2.converter.gson  Body retrofit2.http  Header retrofit2.http  POST retrofit2.http                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     