{"logs": [{"outputFile": "com.arina.ai.app-mergeDebugResources-64:/values-or/values-or.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\caaa22807b29cbcd058e6ca17cad94f2\\transformed\\material3-1.1.2\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,286,395,510,596,692,806,945,1067,1214,1295,1396,1488,1579,1691,1817,1923,2062,2196,2324,2513,2636,2761,2893,3018,3111,3203,3316,3434,3535,3636,3735,3872,4018,4121,4225,4296,4380,4470,4557,4658,4734,4815,4912,5014,5103,5200,5283,5387,5482,5580,5700,5776,5875", "endColumns": "117,112,108,114,85,95,113,138,121,146,80,100,91,90,111,125,105,138,133,127,188,122,124,131,124,92,91,112,117,100,100,98,136,145,102,103,70,83,89,86,100,75,80,96,101,88,96,82,103,94,97,119,75,98,89", "endOffsets": "168,281,390,505,591,687,801,940,1062,1209,1290,1391,1483,1574,1686,1812,1918,2057,2191,2319,2508,2631,2756,2888,3013,3106,3198,3311,3429,3530,3631,3730,3867,4013,4116,4220,4291,4375,4465,4552,4653,4729,4810,4907,5009,5098,5195,5278,5382,5477,5575,5695,5771,5870,5960"}, "to": {"startLines": "29,30,31,32,42,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,77,79,88,91,93,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2869,2987,3100,3209,4243,4528,4624,4738,4877,4999,5146,5227,5328,5420,5511,5623,5749,5855,5994,6128,6256,6445,6568,6693,6825,6950,7043,7135,7248,7366,7467,7568,7667,7804,7950,8053,8349,8506,9240,9490,9678,10048,10124,10205,10302,10404,10493,10590,10673,10777,10872,10970,11090,11166,11265", "endColumns": "117,112,108,114,85,95,113,138,121,146,80,100,91,90,111,125,105,138,133,127,188,122,124,131,124,92,91,112,117,100,100,98,136,145,102,103,70,83,89,86,100,75,80,96,101,88,96,82,103,94,97,119,75,98,89", "endOffsets": "2982,3095,3204,3319,4324,4619,4733,4872,4994,5141,5222,5323,5415,5506,5618,5744,5850,5989,6123,6251,6440,6563,6688,6820,6945,7038,7130,7243,7361,7462,7563,7662,7799,7945,8048,8152,8415,8585,9325,9572,9774,10119,10200,10297,10399,10488,10585,10668,10772,10867,10965,11085,11161,11260,11350"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e44b6092d301126dd6e126ccf393f4dd\\transformed\\core-1.12.0\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,260,363,468,569,671,790", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "153,255,358,463,564,666,785,886"}, "to": {"startLines": "33,34,35,36,37,38,39,92", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3324,3427,3529,3632,3737,3838,3940,9577", "endColumns": "102,101,102,104,100,101,118,100", "endOffsets": "3422,3524,3627,3732,3833,3935,4054,9673"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\895477fa5033432c05b79dc01cd2b72e\\transformed\\appcompat-1.6.1\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,334,441,527,631,751,830,911,1002,1095,1198,1293,1393,1486,1581,1677,1768,1858,1947,2057,2161,2267,2378,2482,2600,2763,2869", "endColumns": "118,109,106,85,103,119,78,80,90,92,102,94,99,92,94,95,90,89,88,109,103,105,110,103,117,162,105,89", "endOffsets": "219,329,436,522,626,746,825,906,997,1090,1193,1288,1388,1481,1576,1672,1763,1853,1942,2052,2156,2262,2373,2477,2595,2758,2864,2954"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,224,334,441,527,631,751,830,911,1002,1095,1198,1293,1393,1486,1581,1677,1768,1858,1947,2057,2161,2267,2378,2482,2600,2763,9330", "endColumns": "118,109,106,85,103,119,78,80,90,92,102,94,99,92,94,95,90,89,88,109,103,105,110,103,117,162,105,89", "endOffsets": "219,329,436,522,626,746,825,906,997,1090,1193,1288,1388,1481,1576,1672,1763,1853,1942,2052,2156,2262,2373,2477,2595,2758,2864,9415"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d9b68a39eaa82fe8f489e3031923adb9\\transformed\\ui-release\\res\\values-or\\values-or.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,289,381,481,567,644,742,830,917,987,1057,1135,1217,1287,1370,1437", "endColumns": "96,86,91,99,85,76,97,87,86,69,69,77,81,69,82,66,118", "endOffsets": "197,284,376,476,562,639,737,825,912,982,1052,1130,1212,1282,1365,1432,1551"}, "to": {"startLines": "40,41,75,76,78,80,81,82,83,84,85,86,87,90,94,95,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4059,4156,8157,8249,8420,8590,8667,8765,8853,8940,9010,9080,9158,9420,9779,9862,9929", "endColumns": "96,86,91,99,85,76,97,87,86,69,69,77,81,69,82,66,118", "endOffsets": "4151,4238,8244,8344,8501,8662,8760,8848,8935,9005,9075,9153,9235,9485,9857,9924,10043"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\afd914afebd159dc02db3b680f36ef47\\transformed\\play-services-basement-16.0.1\\res\\values-or\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "198", "endOffsets": "445"}, "to": {"startLines": "43", "startColumns": "4", "startOffsets": "4329", "endColumns": "198", "endOffsets": "4523"}}]}]}