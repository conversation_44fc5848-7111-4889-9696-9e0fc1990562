package com.arina.ai.data.database;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0002\b\u0004\bg\u0018\u00002\u00020\u0001J\u0013\u0010\u0002\u001a\u0004\u0018\u00010\u0003H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u0004J\u0019\u0010\u0005\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u0003H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\bJ\u0019\u0010\t\u001a\u00020\u00062\u0006\u0010\u0007\u001a\u00020\u0003H\u00a7@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\b\u0082\u0002\u0004\n\u0002\b\u0019\u00a8\u0006\n"}, d2 = {"Lcom/arina/ai/data/database/UserPreferencesDao;", "", "getUserPreferences", "Lcom/arina/ai/data/database/UserPreferences;", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "insertUserPreferences", "", "preferences", "(Lcom/arina/ai/data/database/UserPreferences;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "updateUserPreferences", "app_debug"})
@androidx.room.Dao
public abstract interface UserPreferencesDao {
    
    @androidx.room.Query(value = "SELECT * FROM user_preferences WHERE id = 1")
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object getUserPreferences(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.arina.ai.data.database.UserPreferences> $completion);
    
    @androidx.room.Insert(onConflict = 1)
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object insertUserPreferences(@org.jetbrains.annotations.NotNull
    com.arina.ai.data.database.UserPreferences preferences, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
    
    @androidx.room.Update
    @org.jetbrains.annotations.Nullable
    public abstract java.lang.Object updateUserPreferences(@org.jetbrains.annotations.NotNull
    com.arina.ai.data.database.UserPreferences preferences, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion);
}