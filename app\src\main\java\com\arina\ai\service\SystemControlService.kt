package com.arina.ai.service

import android.app.admin.DevicePolicyManager
import android.bluetooth.BluetoothAdapter
import android.bluetooth.BluetoothManager
import android.content.Context
import android.content.Intent
import android.media.AudioManager
import android.net.wifi.WifiManager
import android.os.PowerManager
import android.provider.Settings
import android.telecom.TelecomManager
import dagger.hilt.android.qualifiers.ApplicationContext
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SystemControlService @Inject constructor(
    @ApplicationContext private val context: Context,
    private val devicePolicyManager: DevicePolicyManager,
    private val powerManager: PowerManager,
    private val audioManager: AudioManager,
    private val wifiManager: WifiManager,
    private val bluetoothManager: BluetoothManager,
    private val telecomManager: TelecomManager
) {
    // Screen Control
    fun setScreenBrightness(brightness: Int) {
        try {
            Settings.System.putInt(
                context.contentResolver,
                Settings.System.SCREEN_BRIGHTNESS,
                brightness.coerceIn(0, 255)
            )
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun setScreenTimeout(timeoutMs: Int) {
        try {
            Settings.System.putInt(
                context.contentResolver,
                Settings.System.SCREEN_OFF_TIMEOUT,
                timeoutMs
            )
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun lockScreen() {
        devicePolicyManager.lockNow()
    }

    // Audio Control
    fun setVolume(streamType: Int, volume: Int) {
        audioManager.setStreamVolume(
            streamType,
            volume.coerceIn(0, audioManager.getStreamMaxVolume(streamType)),
            AudioManager.FLAG_SHOW_UI
        )
    }

    fun muteAudio(streamType: Int) {
        audioManager.adjustStreamVolume(
            streamType,
            AudioManager.ADJUST_MUTE,
            AudioManager.FLAG_SHOW_UI
        )
    }

    fun unmuteAudio(streamType: Int) {
        audioManager.adjustStreamVolume(
            streamType,
            AudioManager.ADJUST_UNMUTE,
            AudioManager.FLAG_SHOW_UI
        )
    }

    // WiFi Control
    fun setWifiEnabled(enabled: Boolean) {
        try {
            wifiManager.isWifiEnabled = enabled
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    // Bluetooth Control
    fun setBluetoothEnabled(enabled: Boolean) {
        bluetoothManager.adapter?.let { adapter ->
            if (enabled) adapter.enable() else adapter.disable()
        }
    }

    // Call Control
    fun answerCall() {
        try {
            telecomManager.acceptRingingCall()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun endCall() {
        try {
            telecomManager.endCall()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun makeCall(phoneNumber: String) {
        try {
            val intent = Intent(Intent.ACTION_CALL)
            intent.data = android.net.Uri.parse("tel:$phoneNumber")
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    // Power Management
    fun isDeviceIdle(): Boolean {
        return powerManager.isDeviceIdleMode
    }

    fun isScreenOn(): Boolean {
        return powerManager.isInteractive
    }

    // System Settings
    fun openSettings(settingsAction: String) {
        try {
            val intent = Intent(settingsAction)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            context.startActivity(intent)
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }
} 