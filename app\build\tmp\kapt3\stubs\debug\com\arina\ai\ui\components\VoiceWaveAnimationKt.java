package com.arina.ai.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000\u001a\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0007\n\u0000\n\u0002\u0010\u000b\n\u0000\u001a&\u0010\u0000\u001a\u00020\u00012\b\b\u0002\u0010\u0002\u001a\u00020\u00032\b\b\u0002\u0010\u0004\u001a\u00020\u00052\b\b\u0002\u0010\u0006\u001a\u00020\u0007H\u0007\u00a8\u0006\b"}, d2 = {"VoiceWaveAnimation", "", "modifier", "Landroidx/compose/ui/Modifier;", "amplitude", "", "isListening", "", "app_debug"})
public final class VoiceWaveAnimationKt {
    
    @androidx.compose.runtime.Composable
    public static final void VoiceWaveAnimation(@org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier, float amplitude, boolean isListening) {
    }
}