package com.arina.ai.ui.theme

import androidx.compose.ui.graphics.Color

object ArinaColors {
    // Primary Colors
    val NeonCyan = Color(0xFF00E5FF)
    val NeonMagenta = Color(0xFFFF00FF)
    val NeonYellow = Color(0xFFFFD700)
    val NeonBlue = Color(0xFF00B8FF)
    val NeonPurple = Color(0xFF9D00FF)
    val NeonOrange = Color(0xFFFF6B00)
    val NeonRed = Color(0xFFFF0050)
    val NeonGreen = Color(0xFF00FF9D)

    // Background Colors
    val DeepBlack = Color(0xFF0A0A0F)
    val DarkGray = Color(0xFF1A1A1F)
    val MidnightBlue = Color(0xFF0F1A2A)
    val SpaceBlack = Color(0xFF050510)

    // Text Colors
    val TextPrimary = Color(0xFFE0E0E5)
    val TextSecondary = Color(0xFFA0A0A5)
    val TextDisabled = Color(0xFF606065)

    // Glow Colors
    val CyanGlow = Color(0x4000E5FF)
    val MagentaGlow = Color(0x40FF00FF)
    val VioletGlow = Color(0x409D00FF)

    // Gradient Stops
    val GradientStart = Color(0xFF00E5FF)
    val GradientMid = Color(0xFFFF00FF)
    val GradientEnd = Color(0xFF9D00FF)

    // Status Colors
    val Success = Color(0xFF00FF9D)
    val Warning = Color(0xFFFFD700)
    val Error = Color(0xFFFF0050)
    val Info = Color(0xFF00B8FF)

    // Surface Colors
    val SurfacePrimary = Color(0xFF1A1A1F)
    val SurfaceSecondary = Color(0xFF252530)
    val SurfaceAccent = Color(0xFF2A2A35)
    val SurfaceInteractive = Color(0xFF303040)

    // Border Colors
    val BorderPrimary = Color(0xFF303040)
    val BorderSecondary = Color(0xFF404050)
    val BorderAccent = Color(0xFF505060)

    // Overlay Colors
    val OverlayLight = Color(0x1AFFFFFF)
    val OverlayMedium = Color(0x40FFFFFF)
    val OverlayDark = Color(0x80000000)
} 