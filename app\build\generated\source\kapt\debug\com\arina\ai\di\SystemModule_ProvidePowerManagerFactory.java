package com.arina.ai.di;

import android.content.Context;
import android.os.PowerManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SystemModule_ProvidePowerManagerFactory implements Factory<PowerManager> {
  private final Provider<Context> contextProvider;

  public SystemModule_ProvidePowerManagerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public PowerManager get() {
    return providePowerManager(contextProvider.get());
  }

  public static SystemModule_ProvidePowerManagerFactory create(Provider<Context> contextProvider) {
    return new SystemModule_ProvidePowerManagerFactory(contextProvider);
  }

  public static PowerManager providePowerManager(Context context) {
    return Preconditions.checkNotNullFromProvides(SystemModule.INSTANCE.providePowerManager(context));
  }
}
