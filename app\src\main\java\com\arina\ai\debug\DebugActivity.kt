package com.arina.ai.debug

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.runtime.Composable
import androidx.compose.ui.tooling.preview.Preview
import com.arina.ai.ui.theme.ArinaTheme

class DebugActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            ArinaTheme {
                ComponentPreview()
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ComponentPreviewPreview() {
    ArinaTheme {
        ComponentPreview()
    }
} 