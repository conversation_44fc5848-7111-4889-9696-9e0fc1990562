package com.arina.ai.ui.components

import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import com.airbnb.lottie.compose.*
import com.arina.ai.R

@Composable
fun LottiePersonalityAvatar(
    emotionalState: EmotionalState,
    modifier: Modifier = Modifier
) {
    val composition by rememberLottieComposition(
        spec = when (emotionalState) {
            EmotionalState.CALM -> LottieCompositionSpec.RawRes(R.raw.arina_calm)
            EmotionalState.EXCITED -> LottieCompositionSpec.RawRes(R.raw.arina_excited)
            EmotionalState.FOCUSED -> LottieCompositionSpec.RawRes(R.raw.arina_focused)
            EmotionalState.CONCERNED -> LottieCompositionSpec.RawRes(R.raw.arina_concerned)
            EmotionalState.CONFUSED -> LottieCompositionSpec.RawRes(R.raw.arina_confused)
        }
    )

    val progress by animateLottieCompositionAsState(
        composition = composition,
        iterations = LottieConstants.IterateForever,
        isPlaying = true,
        speed = when (emotionalState) {
            EmotionalState.CALM -> 1f
            EmotionalState.EXCITED -> 1.5f
            EmotionalState.FOCUSED -> 0.8f
            EmotionalState.CONCERNED -> 1.2f
            EmotionalState.CONFUSED -> 0.7f
        }
    )

    Box(
        modifier = modifier
            .aspectRatio(1f)
    ) {
        LottieAnimation(
            composition = composition,
            progress = { progress },
            modifier = Modifier.fillMaxSize()
        )
    }
} 