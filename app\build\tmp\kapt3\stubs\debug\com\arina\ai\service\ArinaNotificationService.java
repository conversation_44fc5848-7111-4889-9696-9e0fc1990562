package com.arina.ai.service;

@dagger.hilt.android.AndroidEntryPoint
@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000@\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0010 \n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0002\b\u0002\n\u0002\u0010\b\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0007\u0018\u00002\u00020\u0001B\u0005\u00a2\u0006\u0002\u0010\u0002J\u0006\u0010\r\u001a\u00020\u000eJ\u000e\u0010\u000f\u001a\u00020\u000e2\u0006\u0010\u0010\u001a\u00020\u0011J\u0010\u0010\u0012\u001a\u00020\u000e2\u0006\u0010\u0013\u001a\u00020\u0014H\u0016J\u0010\u0010\u0015\u001a\u00020\u000e2\u0006\u0010\u0013\u001a\u00020\u0014H\u0016R\u001a\u0010\u0003\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00050\u0004X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u001d\u0010\u0007\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00060\u00050\b\u00a2\u0006\b\n\u0000\u001a\u0004\b\t\u0010\nR\u000e\u0010\u000b\u001a\u00020\fX\u0082\u0004\u00a2\u0006\u0002\n\u0000\u00a8\u0006\u0016"}, d2 = {"Lcom/arina/ai/service/ArinaNotificationService;", "Landroid/service/notification/NotificationListenerService;", "()V", "_notifications", "Lkotlinx/coroutines/flow/MutableStateFlow;", "", "Lcom/arina/ai/service/NotificationInfo;", "notifications", "Lkotlinx/coroutines/flow/StateFlow;", "getNotifications", "()Lkotlinx/coroutines/flow/StateFlow;", "serviceScope", "Lkotlinx/coroutines/CoroutineScope;", "clearAllNotifications", "", "clearNotification", "id", "", "onNotificationPosted", "sbn", "Landroid/service/notification/StatusBarNotification;", "onNotificationRemoved", "app_debug"})
public final class ArinaNotificationService extends android.service.notification.NotificationListenerService {
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.CoroutineScope serviceScope = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<java.util.List<com.arina.ai.service.NotificationInfo>> _notifications = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<java.util.List<com.arina.ai.service.NotificationInfo>> notifications = null;
    
    public ArinaNotificationService() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<java.util.List<com.arina.ai.service.NotificationInfo>> getNotifications() {
        return null;
    }
    
    @java.lang.Override
    public void onNotificationPosted(@org.jetbrains.annotations.NotNull
    android.service.notification.StatusBarNotification sbn) {
    }
    
    @java.lang.Override
    public void onNotificationRemoved(@org.jetbrains.annotations.NotNull
    android.service.notification.StatusBarNotification sbn) {
    }
    
    public final void clearNotification(int id) {
    }
    
    public final void clearAllNotifications() {
    }
}