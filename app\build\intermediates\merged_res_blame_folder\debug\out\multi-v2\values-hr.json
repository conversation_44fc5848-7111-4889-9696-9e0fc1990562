{"logs": [{"outputFile": "com.arina.ai.app-mergeDebugResources-64:/values-hr/values-hr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\895477fa5033432c05b79dc01cd2b72e\\transformed\\appcompat-1.6.1\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,2816", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,2896"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,89", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,412,498,602,721,806,888,979,1072,1167,1261,1361,1454,1549,1644,1735,1826,1912,2016,2128,2229,2334,2448,2550,2719,9212", "endColumns": "104,94,106,85,103,118,84,81,90,92,94,93,99,92,94,94,90,90,85,103,111,100,104,113,101,168,96,84", "endOffsets": "205,300,407,493,597,716,801,883,974,1067,1162,1256,1356,1449,1544,1639,1730,1821,1907,2011,2123,2224,2329,2443,2545,2714,2811,9292"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\afd914afebd159dc02db3b680f36ef47\\transformed\\play-services-basement-16.0.1\\res\\values-hr\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "247", "endColumns": "190", "endOffsets": "437"}, "to": {"startLines": "43", "startColumns": "4", "startOffsets": "4275", "endColumns": "190", "endOffsets": "4461"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d9b68a39eaa82fe8f489e3031923adb9\\transformed\\ui-release\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,297,391,490,580,659,752,847,932,1004,1075,1156,1242,1315,1394,1464", "endColumns": "104,86,93,98,89,78,92,94,84,71,70,80,85,72,78,69,117", "endOffsets": "205,292,386,485,575,654,747,842,927,999,1070,1151,1237,1310,1389,1459,1577"}, "to": {"startLines": "40,41,75,76,78,80,81,82,83,84,85,86,87,90,94,95,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4008,4113,8019,8113,8293,8460,8539,8632,8727,8812,8884,8955,9036,9297,9658,9737,9807", "endColumns": "104,86,93,98,89,78,92,94,84,71,70,80,85,72,78,69,117", "endOffsets": "4108,4195,8108,8207,8378,8534,8627,8722,8807,8879,8950,9031,9117,9365,9732,9802,9920"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\caaa22807b29cbcd058e6ca17cad94f2\\transformed\\material3-1.1.2\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,288,404,521,596,686,794,931,1046,1187,1268,1364,1455,1549,1664,1786,1887,2019,2150,2280,2444,2566,2686,2811,2932,3024,3118,3244,3374,3467,3565,3670,3806,3949,4054,4149,4230,4307,4397,4479,4584,4668,4747,4840,4937,5026,5125,5209,5310,5403,5499,5633,5719,5815", "endColumns": "115,116,115,116,74,89,107,136,114,140,80,95,90,93,114,121,100,131,130,129,163,121,119,124,120,91,93,125,129,92,97,104,135,142,104,94,80,76,89,81,104,83,78,92,96,88,98,83,100,92,95,133,85,95,87", "endOffsets": "166,283,399,516,591,681,789,926,1041,1182,1263,1359,1450,1544,1659,1781,1882,2014,2145,2275,2439,2561,2681,2806,2927,3019,3113,3239,3369,3462,3560,3665,3801,3944,4049,4144,4225,4302,4392,4474,4579,4663,4742,4835,4932,5021,5120,5204,5305,5398,5494,5628,5714,5810,5898"}, "to": {"startLines": "29,30,31,32,42,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,77,79,88,91,93,97,98,99,100,101,102,103,104,105,106,107,108,109,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2816,2932,3049,3165,4200,4466,4556,4664,4801,4916,5057,5138,5234,5325,5419,5534,5656,5757,5889,6020,6150,6314,6436,6556,6681,6802,6894,6988,7114,7244,7337,7435,7540,7676,7819,7924,8212,8383,9122,9370,9553,9925,10009,10088,10181,10278,10367,10466,10550,10651,10744,10840,10974,11060,11156", "endColumns": "115,116,115,116,74,89,107,136,114,140,80,95,90,93,114,121,100,131,130,129,163,121,119,124,120,91,93,125,129,92,97,104,135,142,104,94,80,76,89,81,104,83,78,92,96,88,98,83,100,92,95,133,85,95,87", "endOffsets": "2927,3044,3160,3277,4270,4551,4659,4796,4911,5052,5133,5229,5320,5414,5529,5651,5752,5884,6015,6145,6309,6431,6551,6676,6797,6889,6983,7109,7239,7332,7430,7535,7671,7814,7919,8014,8288,8455,9207,9447,9653,10004,10083,10176,10273,10362,10461,10545,10646,10739,10835,10969,11055,11151,11239"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\e44b6092d301126dd6e126ccf393f4dd\\transformed\\core-1.12.0\\res\\values-hr\\values-hr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,260,357,456,560,664,781", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "148,255,352,451,555,659,776,877"}, "to": {"startLines": "33,34,35,36,37,38,39,92", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3282,3380,3487,3584,3683,3787,3891,9452", "endColumns": "97,106,96,98,103,103,116,100", "endOffsets": "3375,3482,3579,3678,3782,3886,4003,9548"}}]}]}