package com.arina.ai.data.api

import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.Header
import retrofit2.http.POST

data class A4FRequest(
    val prompt: String,
    val model: String,
    val emotionalState: String? = null,
    val contextHistory: List<String>? = null,
    val maxTokens: Int = 150,
    val temperature: Float = 0.7f
)

data class A4FResponse(
    val response: String,
    val emotionalState: String,
    val confidence: Float,
    val processingTime: Long,
    val tokenCount: Int
)

interface A4FService {
    @POST("v1/chat/completions")
    suspend fun generateResponse(
        @Header("Authorization") apiKey: String,
        @Body request: A4FRequest
    ): Response<A4FResponse>
    
    @POST("v1/models")
    suspend fun getAvailableModels(
        @Header("Authorization") apiKey: String
    ): Response<List<String>>
}