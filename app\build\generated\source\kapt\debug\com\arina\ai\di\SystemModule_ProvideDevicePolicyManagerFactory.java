package com.arina.ai.di;

import android.app.admin.DevicePolicyManager;
import android.content.Context;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SystemModule_ProvideDevicePolicyManagerFactory implements Factory<DevicePolicyManager> {
  private final Provider<Context> contextProvider;

  public SystemModule_ProvideDevicePolicyManagerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public DevicePolicyManager get() {
    return provideDevicePolicyManager(contextProvider.get());
  }

  public static SystemModule_ProvideDevicePolicyManagerFactory create(
      Provider<Context> contextProvider) {
    return new SystemModule_ProvideDevicePolicyManagerFactory(contextProvider);
  }

  public static DevicePolicyManager provideDevicePolicyManager(Context context) {
    return Preconditions.checkNotNullFromProvides(SystemModule.INSTANCE.provideDevicePolicyManager(context));
  }
}
