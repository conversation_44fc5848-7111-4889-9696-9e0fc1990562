<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Arina\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Arina\app\src\main\res"><file name="arina_calm" path="C:\Users\<USER>\Desktop\Arina\app\src\main\res\raw\arina_calm.json" qualifiers="" type="raw"/><file name="arina_concerned" path="C:\Users\<USER>\Desktop\Arina\app\src\main\res\raw\arina_concerned.json" qualifiers="" type="raw"/><file name="arina_confused" path="C:\Users\<USER>\Desktop\Arina\app\src\main\res\raw\arina_confused.json" qualifiers="" type="raw"/><file name="arina_excited" path="C:\Users\<USER>\Desktop\Arina\app\src\main\res\raw\arina_excited.json" qualifiers="" type="raw"/><file name="arina_focused" path="C:\Users\<USER>\Desktop\Arina\app\src\main\res\raw\arina_focused.json" qualifiers="" type="raw"/><file path="C:\Users\<USER>\Desktop\Arina\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">ARINA</string><string name="device_admin_description">ARINA needs device admin access for advanced system controls</string><string name="tap_to_speak">Tap to speak</string><string name="listening">Listening…</string><string name="processing">Processing…</string><string name="error_occurred">An error occurred</string><string name="retry">Retry</string><string name="settings">Settings</string><string name="ai_model">AI Model</string><string name="wake_word">Wake Word</string><string name="notifications">Notifications</string><string name="emotional_responses">Emotional Responses</string><string name="voice_settings">Voice Settings</string><string name="voice_speed">Voice Speed</string><string name="voice_pitch">Voice Pitch</string><string name="theme">Theme</string><string name="save">Save</string><string name="cancel">Cancel</string><string name="permission_rationale">ARINA needs these permissions to function properly</string><string name="permission_denied">Required permissions were denied</string><string name="error_no_internet">No internet connection</string><string name="error_api_key">Invalid API key</string><string name="error_model_unavailable">Selected model is unavailable</string><string name="error_voice_recognition">Voice recognition error</string><string name="error_system_control">System control error</string><string name="notification_channel_name">ARINA Assistant</string><string name="notification_channel_description">Notifications from your AI assistant</string><string name="state_calm">Calm</string><string name="state_focused">Focused</string><string name="state_excited">Excited</string><string name="state_concerned">Concerned</string><string name="state_confused">Confused</string></file><file name="device_admin" path="C:\Users\<USER>\Desktop\Arina\app\src\main\res\xml\device_admin.xml" qualifiers="" type="xml"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\Arina\app\src\main\res\drawable\ic_launcher.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="C:\Users\<USER>\Desktop\Arina\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="C:\Users\<USER>\Desktop\Arina\app\src\main\res\drawable\ic_launcher_foreground.xml" qualifiers="" type="drawable"/><file name="ic_launcher" path="C:\Users\<USER>\Desktop\Arina\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="C:\Users\<USER>\Desktop\Arina\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file path="C:\Users\<USER>\Desktop\Arina\app\src\main\res\values\themes.xml" qualifiers=""><style name="Theme.Arina" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        <item name="colorPrimary">@android:color/holo_blue_bright</item>
        <item name="colorPrimaryDark">@android:color/holo_blue_dark</item>
        <item name="colorAccent">@android:color/holo_purple</item>

        
        <item name="android:statusBarColor">@android:color/holo_blue_dark</item>

        
        <item name="android:windowBackground">@android:color/black</item>
    </style></file><file name="backup_rules" path="C:\Users\<USER>\Desktop\Arina\app\src\main\res\xml\backup_rules.xml" qualifiers="" type="xml"/><file name="data_extraction_rules" path="C:\Users\<USER>\Desktop\Arina\app\src\main\res\xml\data_extraction_rules.xml" qualifiers="" type="xml"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Arina\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Arina\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Arina\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\Desktop\Arina\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>