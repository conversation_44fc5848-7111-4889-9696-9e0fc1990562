package com.arina.ai.service

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.speech.RecognitionListener
import android.speech.RecognizerIntent
import android.speech.SpeechRecognizer
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SpeechRecognizer @Inject constructor(
    @ApplicationContext private val context: Context
) {
    private var speechRecognizer: SpeechRecognizer? = null
    private val _recognitionState = MutableStateFlow<RecognitionState>(RecognitionState.Idle)
    val recognitionState: StateFlow<RecognitionState> = _recognitionState

    // STT restart hack variables
    private var restartAttempts = 0
    private val maxRestartAttempts = 3
    private var lastErrorTime = 0L
    private val restartCooldownMs = 1000L // 1 second cooldown between restarts
    private var currentResultCallback: ((String) -> Unit)? = null

    init {
        initializeSpeechRecognizer()
    }

    private fun initializeSpeechRecognizer() {
        try {
            // Clean up existing recognizer
            speechRecognizer?.destroy()

            if (SpeechRecognizer.isRecognitionAvailable(context)) {
                speechRecognizer = SpeechRecognizer.createSpeechRecognizer(context)
                restartAttempts = 0 // Reset restart attempts on successful initialization
            } else {
                _recognitionState.value = RecognitionState.Error("Speech recognition not available")
            }
        } catch (e: Exception) {
            _recognitionState.value = RecognitionState.Error("Failed to initialize speech recognizer: ${e.message}")
        }
    }

    private fun restartSpeechRecognizer() {
        val currentTime = System.currentTimeMillis()

        // Check cooldown period
        if (currentTime - lastErrorTime < restartCooldownMs) {
            return
        }

        lastErrorTime = currentTime
        restartAttempts++

        if (restartAttempts <= maxRestartAttempts) {
            _recognitionState.value = RecognitionState.Error("Restarting speech recognition... (attempt $restartAttempts)")

            // Reinitialize the speech recognizer
            initializeSpeechRecognizer()

            // If we have a pending callback, restart listening
            currentResultCallback?.let { callback ->
                // Small delay before restarting
                android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                    startListening(callback)
                }, 500)
            }
        } else {
            _recognitionState.value = RecognitionState.Error("Speech recognition failed after $maxRestartAttempts attempts")
            currentResultCallback = null
        }
    }

    fun startListening(onResult: (String) -> Unit) {
        // Store the callback for potential restart
        currentResultCallback = onResult

        speechRecognizer?.let { recognizer ->
            recognizer.setRecognitionListener(object : RecognitionListener {
                override fun onReadyForSpeech(params: Bundle?) {
                    _recognitionState.value = RecognitionState.Ready
                    restartAttempts = 0 // Reset restart attempts on successful start
                }

                override fun onBeginningOfSpeech() {
                    _recognitionState.value = RecognitionState.Listening
                }

                override fun onRmsChanged(rmsdB: Float) {
                    _recognitionState.value = RecognitionState.ListeningWithLevel(rmsdB)
                }

                override fun onBufferReceived(buffer: ByteArray?) {
                    // Buffer received, no action needed
                }

                override fun onEndOfSpeech() {
                    _recognitionState.value = RecognitionState.ProcessingInput
                }

                override fun onError(error: Int) {
                    val errorMessage = when (error) {
                        SpeechRecognizer.ERROR_AUDIO -> "Audio recording error"
                        SpeechRecognizer.ERROR_CLIENT -> "Client side error"
                        SpeechRecognizer.ERROR_INSUFFICIENT_PERMISSIONS -> "Insufficient permissions"
                        SpeechRecognizer.ERROR_NETWORK -> "Network error"
                        SpeechRecognizer.ERROR_NETWORK_TIMEOUT -> "Network timeout"
                        SpeechRecognizer.ERROR_NO_MATCH -> "No speech input"
                        SpeechRecognizer.ERROR_RECOGNIZER_BUSY -> "Recognition service busy"
                        SpeechRecognizer.ERROR_SERVER -> "Server error"
                        SpeechRecognizer.ERROR_SPEECH_TIMEOUT -> "No speech input"
                        else -> "Unknown error"
                    }

                    // STT restart hack: attempt to restart on certain errors
                    val shouldRestart = when (error) {
                        SpeechRecognizer.ERROR_AUDIO,
                        SpeechRecognizer.ERROR_CLIENT,
                        SpeechRecognizer.ERROR_RECOGNIZER_BUSY,
                        SpeechRecognizer.ERROR_SERVER -> true
                        else -> false
                    }

                    if (shouldRestart) {
                        restartSpeechRecognizer()
                    } else {
                        _recognitionState.value = RecognitionState.Error(errorMessage)
                        currentResultCallback = null
                    }
                }

                override fun onResults(results: Bundle?) {
                    val matches = results?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
                    if (!matches.isNullOrEmpty()) {
                        _recognitionState.value = RecognitionState.Success(matches[0])
                        onResult(matches[0])
                        currentResultCallback = null // Clear callback after successful result
                        restartAttempts = 0 // Reset restart attempts on success
                    } else {
                        _recognitionState.value = RecognitionState.Error("No results found")
                        // Don't restart for no results, just clear callback
                        currentResultCallback = null
                    }
                }

                override fun onPartialResults(partialResults: Bundle?) {
                    val matches = partialResults?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
                    if (!matches.isNullOrEmpty()) {
                        _recognitionState.value = RecognitionState.PartialResult(matches[0])
                    }
                }

                override fun onEvent(eventType: Int, params: Bundle?) {
                    // Event received, no action needed
                }
            })

            val recognizerIntent = Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH).apply {
                putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM)
                putExtra(RecognizerIntent.EXTRA_LANGUAGE, Locale.getDefault())
                putExtra(RecognizerIntent.EXTRA_PARTIAL_RESULTS, true)
                putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 1)
            }

            recognizer.startListening(recognizerIntent)
        }
    }

    fun stopListening() {
        speechRecognizer?.stopListening()
        _recognitionState.value = RecognitionState.Idle
        currentResultCallback = null // Clear callback when stopping
    }

    fun release() {
        speechRecognizer?.destroy()
        speechRecognizer = null
        currentResultCallback = null
        restartAttempts = 0
    }

    // Method to force restart STT (can be called externally if needed)
    fun forceRestart() {
        restartAttempts = 0 // Reset attempts for forced restart
        restartSpeechRecognizer()
    }
}

sealed class RecognitionState {
    object Idle : RecognitionState()
    object Ready : RecognitionState()
    object Listening : RecognitionState()
    data class ListeningWithLevel(val rmsDB: Float) : RecognitionState()
    object ProcessingInput : RecognitionState()
    data class PartialResult(val text: String) : RecognitionState()
    data class Success(val text: String) : RecognitionState()
    data class Error(val message: String) : RecognitionState()
} 