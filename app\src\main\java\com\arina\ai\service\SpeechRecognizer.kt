package com.arina.ai.service

import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.speech.RecognitionListener
import android.speech.RecognizerIntent
import android.speech.SpeechRecognizer
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import java.util.*
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class SpeechRecognizer @Inject constructor(
    @ApplicationContext private val context: Context
) {
    private var speechRecognizer: SpeechRecognizer? = null
    private val _recognitionState = MutableStateFlow<RecognitionState>(RecognitionState.Idle)
    val recognitionState: StateFlow<RecognitionState> = _recognitionState

    init {
        initializeSpeechRecognizer()
    }

    private fun initializeSpeechRecognizer() {
        if (SpeechRecognizer.isRecognitionAvailable(context)) {
            speechRecognizer = SpeechRecognizer.createSpeechRecognizer(context)
        } else {
            _recognitionState.value = RecognitionState.Error("Speech recognition not available")
        }
    }

    fun startListening(onResult: (String) -> Unit) {
        speechRecognizer?.let { recognizer ->
            recognizer.setRecognitionListener(object : RecognitionListener {
                override fun onReadyForSpeech(params: Bundle?) {
                    _recognitionState.value = RecognitionState.Ready
                }

                override fun onBeginningOfSpeech() {
                    _recognitionState.value = RecognitionState.Listening
                }

                override fun onRmsChanged(rmsdB: Float) {
                    _recognitionState.value = RecognitionState.ListeningWithLevel(rmsdB)
                }

                override fun onBufferReceived(buffer: ByteArray?) {
                    // Buffer received, no action needed
                }

                override fun onEndOfSpeech() {
                    _recognitionState.value = RecognitionState.ProcessingInput
                }

                override fun onError(error: Int) {
                    val errorMessage = when (error) {
                        SpeechRecognizer.ERROR_AUDIO -> "Audio recording error"
                        SpeechRecognizer.ERROR_CLIENT -> "Client side error"
                        SpeechRecognizer.ERROR_INSUFFICIENT_PERMISSIONS -> "Insufficient permissions"
                        SpeechRecognizer.ERROR_NETWORK -> "Network error"
                        SpeechRecognizer.ERROR_NETWORK_TIMEOUT -> "Network timeout"
                        SpeechRecognizer.ERROR_NO_MATCH -> "No speech input"
                        SpeechRecognizer.ERROR_RECOGNIZER_BUSY -> "Recognition service busy"
                        SpeechRecognizer.ERROR_SERVER -> "Server error"
                        SpeechRecognizer.ERROR_SPEECH_TIMEOUT -> "No speech input"
                        else -> "Unknown error"
                    }
                    _recognitionState.value = RecognitionState.Error(errorMessage)
                }

                override fun onResults(results: Bundle?) {
                    val matches = results?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
                    if (!matches.isNullOrEmpty()) {
                        _recognitionState.value = RecognitionState.Success(matches[0])
                        onResult(matches[0])
                    } else {
                        _recognitionState.value = RecognitionState.Error("No results found")
                    }
                }

                override fun onPartialResults(partialResults: Bundle?) {
                    val matches = partialResults?.getStringArrayList(SpeechRecognizer.RESULTS_RECOGNITION)
                    if (!matches.isNullOrEmpty()) {
                        _recognitionState.value = RecognitionState.PartialResult(matches[0])
                    }
                }

                override fun onEvent(eventType: Int, params: Bundle?) {
                    // Event received, no action needed
                }
            })

            val recognizerIntent = Intent(RecognizerIntent.ACTION_RECOGNIZE_SPEECH).apply {
                putExtra(RecognizerIntent.EXTRA_LANGUAGE_MODEL, RecognizerIntent.LANGUAGE_MODEL_FREE_FORM)
                putExtra(RecognizerIntent.EXTRA_LANGUAGE, Locale.getDefault())
                putExtra(RecognizerIntent.EXTRA_PARTIAL_RESULTS, true)
                putExtra(RecognizerIntent.EXTRA_MAX_RESULTS, 1)
            }

            recognizer.startListening(recognizerIntent)
        }
    }

    fun stopListening() {
        speechRecognizer?.stopListening()
        _recognitionState.value = RecognitionState.Idle
    }

    fun release() {
        speechRecognizer?.destroy()
        speechRecognizer = null
    }
}

sealed class RecognitionState {
    object Idle : RecognitionState()
    object Ready : RecognitionState()
    object Listening : RecognitionState()
    data class ListeningWithLevel(val rmsDB: Float) : RecognitionState()
    object ProcessingInput : RecognitionState()
    data class PartialResult(val text: String) : RecognitionState()
    data class Success(val text: String) : RecognitionState()
    data class Error(val message: String) : RecognitionState()
} 