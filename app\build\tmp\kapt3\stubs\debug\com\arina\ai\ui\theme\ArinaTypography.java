package com.arina.ai.ui.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u001f\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u0011\u0010\u0003\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006R\u0011\u0010\u0007\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\b\u0010\u0006R\u0011\u0010\t\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\n\u0010\u0006R\u0011\u0010\u000b\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\f\u0010\u0006R\u0011\u0010\r\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u0006R\u0011\u0010\u000f\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0010\u0010\u0006R\u0011\u0010\u0011\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0012\u0010\u0006R\u0011\u0010\u0013\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0014\u0010\u0006R\u0011\u0010\u0015\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0016\u0010\u0006R\u0011\u0010\u0017\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0018\u0010\u0006R\u0011\u0010\u0019\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001a\u0010\u0006R\u0011\u0010\u001b\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001c\u0010\u0006R\u0011\u0010\u001d\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\u001e\u0010\u0006R\u0011\u0010\u001f\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b \u0010\u0006R\u0011\u0010!\u001a\u00020\u0004\u00a2\u0006\b\n\u0000\u001a\u0004\b\"\u0010\u0006\u00a8\u0006#"}, d2 = {"Lcom/arina/ai/ui/theme/ArinaTypography;", "", "()V", "bodyLarge", "Landroidx/compose/ui/text/TextStyle;", "getBodyLarge", "()Landroidx/compose/ui/text/TextStyle;", "bodyMedium", "getBodyMedium", "bodySmall", "getBodySmall", "displayLarge", "getDisplayLarge", "displayMedium", "getDisplayMedium", "displaySmall", "getDisplaySmall", "headlineLarge", "getHeadlineLarge", "headlineMedium", "getHeadlineMedium", "headlineSmall", "getHeadlineSmall", "labelLarge", "getLabelLarge", "labelMedium", "getLabelMedium", "labelSmall", "getLabelSmall", "titleLarge", "getTitleLarge", "titleMedium", "getTitleMedium", "titleSmall", "getTitleSmall", "app_debug"})
public final class ArinaTypography {
    @org.jetbrains.annotations.NotNull
    private static final androidx.compose.ui.text.TextStyle displayLarge = null;
    @org.jetbrains.annotations.NotNull
    private static final androidx.compose.ui.text.TextStyle displayMedium = null;
    @org.jetbrains.annotations.NotNull
    private static final androidx.compose.ui.text.TextStyle displaySmall = null;
    @org.jetbrains.annotations.NotNull
    private static final androidx.compose.ui.text.TextStyle headlineLarge = null;
    @org.jetbrains.annotations.NotNull
    private static final androidx.compose.ui.text.TextStyle headlineMedium = null;
    @org.jetbrains.annotations.NotNull
    private static final androidx.compose.ui.text.TextStyle headlineSmall = null;
    @org.jetbrains.annotations.NotNull
    private static final androidx.compose.ui.text.TextStyle titleLarge = null;
    @org.jetbrains.annotations.NotNull
    private static final androidx.compose.ui.text.TextStyle titleMedium = null;
    @org.jetbrains.annotations.NotNull
    private static final androidx.compose.ui.text.TextStyle titleSmall = null;
    @org.jetbrains.annotations.NotNull
    private static final androidx.compose.ui.text.TextStyle labelLarge = null;
    @org.jetbrains.annotations.NotNull
    private static final androidx.compose.ui.text.TextStyle labelMedium = null;
    @org.jetbrains.annotations.NotNull
    private static final androidx.compose.ui.text.TextStyle labelSmall = null;
    @org.jetbrains.annotations.NotNull
    private static final androidx.compose.ui.text.TextStyle bodyLarge = null;
    @org.jetbrains.annotations.NotNull
    private static final androidx.compose.ui.text.TextStyle bodyMedium = null;
    @org.jetbrains.annotations.NotNull
    private static final androidx.compose.ui.text.TextStyle bodySmall = null;
    @org.jetbrains.annotations.NotNull
    public static final com.arina.ai.ui.theme.ArinaTypography INSTANCE = null;
    
    private ArinaTypography() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.compose.ui.text.TextStyle getDisplayLarge() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.compose.ui.text.TextStyle getDisplayMedium() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.compose.ui.text.TextStyle getDisplaySmall() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.compose.ui.text.TextStyle getHeadlineLarge() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.compose.ui.text.TextStyle getHeadlineMedium() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.compose.ui.text.TextStyle getHeadlineSmall() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.compose.ui.text.TextStyle getTitleLarge() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.compose.ui.text.TextStyle getTitleMedium() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.compose.ui.text.TextStyle getTitleSmall() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.compose.ui.text.TextStyle getLabelLarge() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.compose.ui.text.TextStyle getLabelMedium() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.compose.ui.text.TextStyle getLabelSmall() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.compose.ui.text.TextStyle getBodyLarge() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.compose.ui.text.TextStyle getBodyMedium() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final androidx.compose.ui.text.TextStyle getBodySmall() {
        return null;
    }
}