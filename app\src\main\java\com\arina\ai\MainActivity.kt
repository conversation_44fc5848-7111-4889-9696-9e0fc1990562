package com.arina.ai

import android.Manifest
import android.app.admin.DevicePolicyManager
import android.bluetooth.BluetoothManager
import android.content.ComponentName
import android.content.Intent
import android.content.pm.PackageManager
import android.media.AudioManager
import android.net.wifi.WifiManager
import android.os.Bundle
import android.os.PowerManager
import android.speech.tts.TextToSpeech
import android.telecom.TelecomManager
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.core.content.ContextCompat
import com.arina.ai.data.api.A4FService
import com.arina.ai.data.database.ArinaDatabase
import com.arina.ai.data.repository.ArinaRepository
import com.arina.ai.receiver.ArinaDeviceAdminReceiver
import com.arina.ai.service.SystemControlService
import com.arina.ai.service.VoiceInteractionService
import com.arina.ai.ui.screens.MainScreen
import com.arina.ai.ui.theme.ArinaTheme
import com.arina.ai.viewmodel.MainViewModel
import com.arina.ai.viewmodel.MainViewEvent
import retrofit2.Retrofit
import retrofit2.converter.gson.GsonConverterFactory
import java.util.*

class MainActivity : ComponentActivity() {
    private lateinit var viewModel: MainViewModel
    private lateinit var textToSpeech: TextToSpeech
    private lateinit var systemControlService: SystemControlService
    private lateinit var voiceInteractionService: VoiceInteractionService

    // Essential permissions only - system will show dialogs directly
    private val requiredPermissions = arrayOf(
        Manifest.permission.RECORD_AUDIO,
        Manifest.permission.CAMERA,
        Manifest.permission.CALL_PHONE,
        Manifest.permission.READ_PHONE_STATE,
        Manifest.permission.ANSWER_PHONE_CALLS,
        Manifest.permission.POST_NOTIFICATIONS,
        Manifest.permission.MODIFY_AUDIO_SETTINGS,
        Manifest.permission.READ_EXTERNAL_STORAGE,
        Manifest.permission.WRITE_EXTERNAL_STORAGE
    )

    // Simple permission launcher - no custom screens
    private val permissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        // Continue regardless of permission status - let system handle it
        initializeApp()
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Request permissions directly without complex logic
        if (!hasRequiredPermissions()) {
            permissionLauncher.launch(requiredPermissions)
        } else {
            initializeApp()
        }
    }

    private fun hasRequiredPermissions(): Boolean {
        return requiredPermissions.all { permission ->
            ContextCompat.checkSelfPermission(this, permission) == PackageManager.PERMISSION_GRANTED
        }
    }

    private fun requestSpecialPermissions() {
        // Request SYSTEM_ALERT_WINDOW permission if needed
        if (!android.provider.Settings.canDrawOverlays(this)) {
            val intent = Intent(android.provider.Settings.ACTION_MANAGE_OVERLAY_PERMISSION).apply {
                data = android.net.Uri.parse("package:$packageName")
            }
            try {
                startActivity(intent)
            } catch (e: Exception) {
                // Handle if settings activity is not available
            }
        }

        // Request WRITE_SETTINGS permission if needed
        if (!android.provider.Settings.System.canWrite(this)) {
            val intent = Intent(android.provider.Settings.ACTION_MANAGE_WRITE_SETTINGS).apply {
                data = android.net.Uri.parse("package:$packageName")
            }
            try {
                startActivity(intent)
            } catch (e: Exception) {
                // Handle if settings activity is not available
            }
        }
    }

    private fun initializeApp() {
        // Request special permissions that need separate handling
        requestSpecialPermissions()

        // Initialize TextToSpeech
        textToSpeech = TextToSpeech(this) { status ->
            if (status == TextToSpeech.SUCCESS) {
                textToSpeech.language = Locale.US
            }
        }

        // Initialize SystemControlService with required system services
        systemControlService = SystemControlService(
            context = applicationContext,
            devicePolicyManager = getSystemService(DEVICE_POLICY_SERVICE) as DevicePolicyManager,
            powerManager = getSystemService(POWER_SERVICE) as PowerManager,
            audioManager = getSystemService(AUDIO_SERVICE) as AudioManager,
            wifiManager = applicationContext.getSystemService(WIFI_SERVICE) as WifiManager,
            bluetoothManager = getSystemService(BLUETOOTH_SERVICE) as BluetoothManager,
            telecomManager = getSystemService(TELECOM_SERVICE) as TelecomManager
        )

        // Initialize VoiceInteractionService
        voiceInteractionService = VoiceInteractionService(applicationContext)

        // Initialize Retrofit
        val retrofit = Retrofit.Builder()
            .baseUrl("https://api.a4f.co/")
            .addConverterFactory(GsonConverterFactory.create())
            .build()

        val apiService = retrofit.create(A4FService::class.java)
        
        // Initialize Database
        val database = ArinaDatabase.getInstance(applicationContext)
        
        // Initialize Repository
        val repository = ArinaRepository(database, apiService)
        
        // Initialize ViewModel
        viewModel = MainViewModel(repository, textToSpeech, systemControlService).apply {
            // Set up voice interaction service reference
            setVoiceInteractionService(voiceInteractionService)

            // Set up voice interaction callback
            voiceInteractionService.setVoiceResultCallback { text ->
                handleEvent(MainViewEvent.ProcessVoiceInput(text))
            }
        }

        // Set up the UI
        setContent {
            ArinaTheme {
                MainScreen(
                    viewModel = viewModel,
                    voiceInteractionService = voiceInteractionService,
                    modifier = Modifier.fillMaxSize()
                )
            }
        }

        // Start services
        startService(Intent(this, com.arina.ai.service.ArinaNotificationService::class.java))
        voiceInteractionService.startWakeWordDetection()
    }

    override fun onDestroy() {
        super.onDestroy()
        if (::textToSpeech.isInitialized) {
            textToSpeech.shutdown()
        }
        if (::voiceInteractionService.isInitialized) {
            voiceInteractionService.release()
        }
    }
} 