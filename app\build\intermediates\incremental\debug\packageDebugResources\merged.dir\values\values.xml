<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="ai_model">AI Model</string>
    <string name="app_name">ARINA</string>
    <string name="cancel">Cancel</string>
    <string name="device_admin_description">ARINA needs device admin access for advanced system controls</string>
    <string name="emotional_responses">Emotional Responses</string>
    <string name="error_api_key">Invalid API key</string>
    <string name="error_model_unavailable">Selected model is unavailable</string>
    <string name="error_no_internet">No internet connection</string>
    <string name="error_occurred">An error occurred</string>
    <string name="error_system_control">System control error</string>
    <string name="error_voice_recognition">Voice recognition error</string>
    <string name="listening">Listening…</string>
    <string name="notification_channel_description">Notifications from your AI assistant</string>
    <string name="notification_channel_name">ARINA Assistant</string>
    <string name="notifications">Notifications</string>
    <string name="permission_denied">Required permissions were denied</string>
    <string name="permission_rationale">ARINA needs these permissions to function properly</string>
    <string name="processing">Processing…</string>
    <string name="retry">Retry</string>
    <string name="save">Save</string>
    <string name="settings">Settings</string>
    <string name="state_calm">Calm</string>
    <string name="state_concerned">Concerned</string>
    <string name="state_confused">Confused</string>
    <string name="state_excited">Excited</string>
    <string name="state_focused">Focused</string>
    <string name="tap_to_speak">Tap to speak</string>
    <string name="theme">Theme</string>
    <string name="voice_pitch">Voice Pitch</string>
    <string name="voice_settings">Voice Settings</string>
    <string name="voice_speed">Voice Speed</string>
    <string name="wake_word">Wake Word</string>
    <style name="Theme.Arina" parent="Theme.AppCompat.DayNight.NoActionBar">
        
        <item name="colorPrimary">@android:color/holo_blue_bright</item>
        <item name="colorPrimaryDark">@android:color/holo_blue_dark</item>
        <item name="colorAccent">@android:color/holo_purple</item>

        
        <item name="android:statusBarColor">@android:color/holo_blue_dark</item>

        
        <item name="android:windowBackground">@android:color/black</item>
    </style>
</resources>