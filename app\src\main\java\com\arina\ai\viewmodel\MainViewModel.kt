package com.arina.ai.viewmodel

import android.media.AudioManager
import android.speech.tts.TextToSpeech
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.arina.ai.data.api.A4FResponse
import com.arina.ai.data.database.Conversation
import com.arina.ai.data.database.UserPreferences
import com.arina.ai.data.repository.ArinaRepository
import com.arina.ai.service.SystemControlService
import com.arina.ai.ui.components.EmotionalState
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import java.util.*

data class MainViewState(
    val isListening: Boolean = false,
    val isProcessing: Boolean = false,
    val currentEmotionalState: EmotionalState = EmotionalState.CALM,
    val conversations: List<Conversation> = emptyList(),
    val errorMessage: String? = null,
    val userPreferences: UserPreferences? = null,
    val systemInfo: Map<String, Any> = emptyMap()
)

sealed class MainViewEvent {
    data class StartListening(val wakeWordDetected: Boolean = false) : MainViewEvent()
    object StopListening : MainViewEvent()
    data class ProcessVoiceInput(val text: String) : MainViewEvent()
    data class ProcessTextInput(val text: String) : MainViewEvent()
    data class BookmarkConversation(val conversation: Conversation) : MainViewEvent()
    data class DeleteConversation(val conversation: Conversation) : MainViewEvent()
    data class UpdatePreferences(val preferences: UserPreferences) : MainViewEvent()
    data class ExecuteSystemCommand(val command: SystemCommand) : MainViewEvent()
    object ClearError : MainViewEvent()
}

sealed class SystemCommand {
    data class SetVolume(val streamType: Int = AudioManager.STREAM_MUSIC, val volume: Int) : SystemCommand()
    data class SetMute(val streamType: Int = AudioManager.STREAM_MUSIC, val mute: Boolean) : SystemCommand()
    data class SetWifi(val enabled: Boolean) : SystemCommand()
    data class SetBluetooth(val enabled: Boolean) : SystemCommand()
    data class SetBrightness(val brightness: Int) : SystemCommand()
    data class MakeCall(val number: String) : SystemCommand()
    object AnswerCall : SystemCommand()
    object EndCall : SystemCommand()
    data class OpenSettings(val settingType: String) : SystemCommand()
    data class LaunchApp(val packageName: String) : SystemCommand()
    data class CloseApp(val packageName: String) : SystemCommand()
    object LockScreen : SystemCommand()
    object RefreshSystemInfo : SystemCommand()
}

class MainViewModel(
    private val repository: ArinaRepository,
    private val textToSpeech: TextToSpeech,
    private val systemControlService: SystemControlService
) : ViewModel() {

    private val _viewState = MutableStateFlow(MainViewState())
    val viewState: StateFlow<MainViewState> = _viewState.asStateFlow()

    init {
        viewModelScope.launch {
            // Load initial preferences
            repository.getUserPreferences()?.let { prefs ->
                _viewState.update { it.copy(userPreferences = prefs) }
            }

            // Load conversation history
            repository.getConversationHistory()
                .catch { e ->
                    _viewState.update { 
                        it.copy(errorMessage = "Failed to load conversations: ${e.message}")
                    }
                }
                .collect { conversations ->
                    _viewState.update { it.copy(conversations = conversations) }
                }

            // Load initial system info
            refreshSystemInfo()
        }
    }

    fun handleEvent(event: MainViewEvent) {
        when (event) {
            is MainViewEvent.StartListening -> startListening(event.wakeWordDetected)
            is MainViewEvent.StopListening -> stopListening()
            is MainViewEvent.ProcessVoiceInput -> processUserInput(event.text, isVoice = true)
            is MainViewEvent.ProcessTextInput -> processUserInput(event.text, isVoice = false)
            is MainViewEvent.BookmarkConversation -> bookmarkConversation(event.conversation)
            is MainViewEvent.DeleteConversation -> deleteConversation(event.conversation)
            is MainViewEvent.UpdatePreferences -> updatePreferences(event.preferences)
            is MainViewEvent.ExecuteSystemCommand -> executeSystemCommand(event.command)
            is MainViewEvent.ClearError -> clearError()
        }
    }

    private fun startListening(wakeWordDetected: Boolean) {
        _viewState.update { 
            it.copy(
                isListening = true,
                currentEmotionalState = EmotionalState.FOCUSED
            )
        }
    }

    private fun stopListening() {
        _viewState.update { it.copy(isListening = false) }
    }

    private fun processUserInput(text: String, isVoice: Boolean) {
        viewModelScope.launch {
            _viewState.update { 
                it.copy(
                    isProcessing = true,
                    currentEmotionalState = EmotionalState.FOCUSED
                )
            }

            try {
                val prefs = _viewState.value.userPreferences
                if (prefs == null) {
                    _viewState.update { 
                        it.copy(errorMessage = "Please configure AI settings first")
                    }
                    return@launch
                }

                // Get conversation history for context
                val recentConversations = _viewState.value.conversations
                    .take(5)
                    .map { "${it.userMessage} -> ${it.arinaResponse}" }

                val response = repository.generateResponse(
                    prompt = text,
                    model = prefs.selectedModel,
                    contextHistory = recentConversations
                )

                response.fold(
                    onSuccess = { aiResponse ->
                        handleSuccessfulResponse(aiResponse, isVoice)
                    },
                    onFailure = { error ->
                        _viewState.update {
                            it.copy(
                                isProcessing = false,
                                errorMessage = "Failed to process input: ${error.message}",
                                currentEmotionalState = EmotionalState.CONFUSED
                            )
                        }
                    }
                )
            } catch (e: Exception) {
                _viewState.update {
                    it.copy(
                        isProcessing = false,
                        errorMessage = "An error occurred: ${e.message}",
                        currentEmotionalState = EmotionalState.CONFUSED
                    )
                }
            }
        }
    }

    private fun handleSuccessfulResponse(response: A4FResponse, speakResponse: Boolean) {
        val emotionalState = try {
            EmotionalState.valueOf(response.emotionalState.uppercase())
        } catch (e: Exception) {
            EmotionalState.CALM
        }

        _viewState.update {
            it.copy(
                isProcessing = false,
                currentEmotionalState = emotionalState
            )
        }

        if (speakResponse && _viewState.value.userPreferences?.voiceSpeed != null) {
            textToSpeech.setSpeechRate(_viewState.value.userPreferences?.voiceSpeed ?: 1.0f)
            textToSpeech.setPitch(_viewState.value.userPreferences?.voicePitch ?: 1.0f)
            textToSpeech.speak(
                response.response,
                TextToSpeech.QUEUE_FLUSH,
                null,
                UUID.randomUUID().toString()
            )
        }
    }

    private fun bookmarkConversation(conversation: Conversation) {
        viewModelScope.launch {
            try {
                repository.bookmarkConversation(conversation)
            } catch (e: Exception) {
                _viewState.update {
                    it.copy(errorMessage = "Failed to bookmark conversation: ${e.message}")
                }
            }
        }
    }

    private fun deleteConversation(conversation: Conversation) {
        viewModelScope.launch {
            try {
                repository.deleteConversation(conversation)
            } catch (e: Exception) {
                _viewState.update {
                    it.copy(errorMessage = "Failed to delete conversation: ${e.message}")
                }
            }
        }
    }

    private fun updatePreferences(preferences: UserPreferences) {
        viewModelScope.launch {
            try {
                repository.updateUserPreferences(preferences)
                _viewState.update { it.copy(userPreferences = preferences) }
            } catch (e: Exception) {
                _viewState.update {
                    it.copy(errorMessage = "Failed to update preferences: ${e.message}")
                }
            }
        }
    }

    private fun executeSystemCommand(command: SystemCommand) {
        try {
            when (command) {
                is SystemCommand.SetVolume -> systemControlService.setVolume(command.streamType, command.volume)
                is SystemCommand.SetMute -> systemControlService.setMute(command.streamType, command.mute)
                is SystemCommand.SetWifi -> systemControlService.setWifiEnabled(command.enabled)
                is SystemCommand.SetBluetooth -> systemControlService.setBluetoothEnabled(command.enabled)
                is SystemCommand.SetBrightness -> systemControlService.setBrightness(command.brightness)
                is SystemCommand.MakeCall -> systemControlService.makePhoneCall(command.number)
                is SystemCommand.AnswerCall -> systemControlService.answerCall()
                is SystemCommand.EndCall -> systemControlService.endCall()
                is SystemCommand.OpenSettings -> systemControlService.openSettings(command.settingType)
                is SystemCommand.LaunchApp -> systemControlService.launchApp(command.packageName)
                is SystemCommand.CloseApp -> systemControlService.closeApp(command.packageName)
                is SystemCommand.LockScreen -> systemControlService.lockScreen()
                is SystemCommand.RefreshSystemInfo -> refreshSystemInfo()
            }
        } catch (e: Exception) {
            _viewState.update {
                it.copy(errorMessage = "Failed to execute system command: ${e.message}")
            }
        }
    }

    private fun refreshSystemInfo() {
        viewModelScope.launch {
            try {
                val systemInfo = systemControlService.getSystemInfo()
                _viewState.update { it.copy(systemInfo = systemInfo) }
            } catch (e: Exception) {
                _viewState.update {
                    it.copy(errorMessage = "Failed to refresh system info: ${e.message}")
                }
            }
        }
    }

    private fun clearError() {
        _viewState.update { it.copy(errorMessage = null) }
    }

    override fun onCleared() {
        super.onCleared()
        textToSpeech.shutdown()
    }
} 