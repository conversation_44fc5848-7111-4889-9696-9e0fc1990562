package com.arina.ai.ui.theme

import androidx.compose.foundation.shape.GenericShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Shapes
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.Outline
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.Shape
import androidx.compose.ui.unit.Density
import androidx.compose.ui.unit.LayoutDirection
import androidx.compose.ui.unit.dp
import kotlin.math.min

val ArinaShapes = Shapes(
    small = RoundedCornerShape(4.dp),
    medium = RoundedCornerShape(8.dp),
    large = RoundedCornerShape(12.dp)
)

class CyberShape(private val cornerSize: Float = 0.2f) : Shape {
    override fun createOutline(
        size: Size,
        layoutDirection: LayoutDirection,
        density: Density
    ): Outline {
        val path = Path().apply {
            val width = size.width
            val height = size.height
            val cornerWidth = width * cornerSize
            val cornerHeight = height * cornerSize

            // Start from top-left with a cyber corner
            moveTo(0f, cornerHeight)
            lineTo(cornerWidth, 0f)
            
            // Top edge
            lineTo(width - cornerWidth, 0f)
            
            // Top-right cyber corner
            lineTo(width, cornerHeight)
            
            // Right edge
            lineTo(width, height - cornerHeight)
            
            // Bottom-right cyber corner
            lineTo(width - cornerWidth, height)
            
            // Bottom edge
            lineTo(cornerWidth, height)
            
            // Bottom-left cyber corner
            lineTo(0f, height - cornerHeight)
            
            // Close the path
            close()
        }
        return Outline.Generic(path)
    }
}

class HexagonShape : Shape {
    override fun createOutline(
        size: Size,
        layoutDirection: LayoutDirection,
        density: Density
    ): Outline {
        val path = Path().apply {
            val width = size.width
            val height = size.height
            val sideLength = min(width, height) / 2

            // Start from top point
            moveTo(width / 2, 0f)
            
            // Top-right point
            lineTo(width / 2 + sideLength * 0.866f, height / 4)
            
            // Bottom-right point
            lineTo(width / 2 + sideLength * 0.866f, height * 3 / 4)
            
            // Bottom point
            lineTo(width / 2, height)
            
            // Bottom-left point
            lineTo(width / 2 - sideLength * 0.866f, height * 3 / 4)
            
            // Top-left point
            lineTo(width / 2 - sideLength * 0.866f, height / 4)
            
            // Close the path
            close()
        }
        return Outline.Generic(path)
    }
}

class DiagonalCutShape(private val cutSize: Float = 0.2f) : Shape {
    override fun createOutline(
        size: Size,
        layoutDirection: LayoutDirection,
        density: Density
    ): Outline {
        val path = Path().apply {
            val width = size.width
            val height = size.height
            val cut = min(width, height) * cutSize

            // Start from top-left after the cut
            moveTo(cut, 0f)
            
            // Top edge
            lineTo(width - cut, 0f)
            
            // Top-right cut
            lineTo(width, cut)
            
            // Right edge
            lineTo(width, height - cut)
            
            // Bottom-right cut
            lineTo(width - cut, height)
            
            // Bottom edge
            lineTo(cut, height)
            
            // Bottom-left cut
            lineTo(0f, height - cut)
            
            // Left edge
            lineTo(0f, cut)
            
            // Close the path
            close()
        }
        return Outline.Generic(path)
    }
}

val CyberButton = CyberShape(0.2f)
val CyberCard = CyberShape(0.1f)
val CyberDialog = CyberShape(0.05f)
val HexagonAvatar = HexagonShape()
val DiagonalCard = DiagonalCutShape(0.1f) 