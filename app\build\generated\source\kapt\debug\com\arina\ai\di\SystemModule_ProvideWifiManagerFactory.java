package com.arina.ai.di;

import android.content.Context;
import android.net.wifi.WifiManager;
import dagger.internal.DaggerGenerated;
import dagger.internal.Factory;
import dagger.internal.Preconditions;
import dagger.internal.QualifierMetadata;
import dagger.internal.ScopeMetadata;
import javax.annotation.processing.Generated;
import javax.inject.Provider;

@ScopeMetadata("javax.inject.Singleton")
@QualifierMetadata("dagger.hilt.android.qualifiers.ApplicationContext")
@DaggerGenerated
@Generated(
    value = "dagger.internal.codegen.ComponentProcessor",
    comments = "https://dagger.dev"
)
@SuppressWarnings({
    "unchecked",
    "rawtypes",
    "KotlinInternal",
    "KotlinInternalInJava"
})
public final class SystemModule_ProvideWifiManagerFactory implements Factory<WifiManager> {
  private final Provider<Context> contextProvider;

  public SystemModule_ProvideWifiManagerFactory(Provider<Context> contextProvider) {
    this.contextProvider = contextProvider;
  }

  @Override
  public WifiManager get() {
    return provideWifiManager(contextProvider.get());
  }

  public static SystemModule_ProvideWifiManagerFactory create(Provider<Context> contextProvider) {
    return new SystemModule_ProvideWifiManagerFactory(contextProvider);
  }

  public static WifiManager provideWifiManager(Context context) {
    return Preconditions.checkNotNullFromProvides(SystemModule.INSTANCE.provideWifiManager(context));
  }
}
