package com.arina.ai.ui.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.unit.dp
import com.arina.ai.ui.theme.ArinaColors
import kotlin.math.sin

@Composable
fun VoiceWaveform(
    amplitude: Float,
    modifier: Modifier = Modifier,
    color: Color = ArinaColors.NeonCyan,
    waveCount: Int = 50
) {
    val infiniteTransition = rememberInfiniteTransition()
    
    val phase by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 2f * Math.PI.toFloat(),
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        )
    )

    Canvas(
        modifier = modifier
            .fillMaxWidth()
            .height(60.dp)
    ) {
        val width = size.width
        val height = size.height
        val centerY = height / 2
        val segmentWidth = width / waveCount

        for (i in 0 until waveCount) {
            val x = i * segmentWidth
            val wavePhase = phase + (i.toFloat() / waveCount) * 2f * Math.PI.toFloat()
            val waveAmplitude = sin(wavePhase).toFloat() * amplitude
            val y = centerY + (waveAmplitude * height / 3)

            drawLine(
                color = color.copy(alpha = 0.7f),
                start = Offset(x, centerY),
                end = Offset(x, y),
                strokeWidth = 3.dp.toPx(),
                cap = StrokeCap.Round
            )

            // Draw glow effect
            drawLine(
                color = color.copy(alpha = 0.3f),
                start = Offset(x, centerY),
                end = Offset(x, y),
                strokeWidth = 6.dp.toPx(),
                cap = StrokeCap.Round
            )
        }
    }
}

@Composable
fun VoiceWaveformWithState(
    state: RecognitionState,
    modifier: Modifier = Modifier
) {
    val amplitude = when (state) {
        is RecognitionState.ListeningWithLevel -> {
            // Convert RMS dB to amplitude (0.0 to 1.0)
            (state.rmsDB + 100) / 100f
        }
        RecognitionState.Listening -> 0.5f
        RecognitionState.ProcessingInput -> 0.8f
        else -> 0.2f
    }

    val color = when (state) {
        is RecognitionState.Error -> ArinaColors.NeonRed
        is RecognitionState.Success -> ArinaColors.NeonGreen
        is RecognitionState.ProcessingInput -> ArinaColors.NeonMagenta
        else -> ArinaColors.NeonCyan
    }

    VoiceWaveform(
        amplitude = amplitude,
        color = color,
        modifier = modifier
    )
}