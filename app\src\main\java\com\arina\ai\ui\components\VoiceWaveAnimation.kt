package com.arina.ai.ui.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.size
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.unit.dp
import com.arina.ai.ui.theme.ArinaColors
import kotlin.math.PI
import kotlin.math.sin

@Composable
fun VoiceWaveAnimation(
    modifier: Modifier = Modifier,
    amplitude: Float = 1f,
    isListening: Boolean = false
) {
    val infiniteTransition = rememberInfiniteTransition(label = "voiceWave")
    
    val wavePhase by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 2 * PI.toFloat(),
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        ),
        label = "wavePhase"
    )

    val glowAlpha by infiniteTransition.animateFloat(
        initialValue = 0.3f,
        targetValue = 0.6f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = LinearEasing),
            repeatMode = RepeatMode.Reverse
        ),
        label = "glowAlpha"
    )

    Canvas(
        modifier = modifier.size(200.dp)
    ) {
        val width = size.width
        val height = size.height
        val centerY = height / 2

        // Draw multiple waves with different phases and amplitudes
        for (i in 0..2) {
            val path = Path()
            val phase = wavePhase + i * PI.toFloat() / 3
            val waveAmplitude = if (isListening) amplitude * (3 - i) * 20 else 5f
            
            path.moveTo(0f, centerY)
            for (x in 0..width.toInt() step 5) {
                val y = centerY + sin(x * 0.03f + phase) * waveAmplitude
                path.lineTo(x.toFloat(), y)
            }

            // Create gradient brush for wave
            val gradient = Brush.linearGradient(
                colors = listOf(
                    ArinaColors.NeonCyan.copy(alpha = glowAlpha * (1f - i * 0.2f)),
                    ArinaColors.NeonMagenta.copy(alpha = glowAlpha * (1f - i * 0.2f))
                ),
                start = Offset(0f, 0f),
                end = Offset(width, 0f)
            )

            // Draw wave with glow effect
            drawPath(
                path = path,
                brush = gradient,
                style = Stroke(
                    width = (4 - i).dp.toPx(),
                    cap = StrokeCap.Round
                )
            )
        }

        // Draw center circle
        drawCircle(
            brush = Brush.radialGradient(
                colors = listOf(
                    ArinaColors.NeonCyan.copy(alpha = glowAlpha),
                    Color.Transparent
                ),
                center = Offset(width / 2, height / 2),
                radius = 30f
            ),
            radius = 30f,
            center = Offset(width / 2, height / 2)
        )
    }
} 