package com.arina.ai.data.database

import androidx.room.*
import java.util.*

@Entity(tableName = "conversations")
data class Conversation(
    @PrimaryKey val id: String = UUID.randomUUID().toString(),
    val timestamp: Long = System.currentTimeMillis(),
    val userMessage: String,
    val arinaResponse: String,
    val emotionalState: String,
    val importance: Int = 1,
    val isBookmarked: Boolean = false
)

@Entity(tableName = "user_preferences")
data class UserPreferences(
    @PrimaryKey val id: Int = 1, // Single row for preferences
    val selectedModel: String,
    val wakeWordEnabled: Boolean = true,
    val notificationSpeakerEnabled: Boolean = false,
    val emotionalResponsesEnabled: Boolean = true,
    val voiceSpeed: Float = 1.0f,
    val voicePitch: Float = 1.0f,
    val theme: String = "CYBERPUNK"
)

@Dao
interface ConversationDao {
    @Query("SELECT * FROM conversations ORDER BY timestamp DESC")
    suspend fun getAllConversations(): List<Conversation>
    
    @Query("SELECT * FROM conversations WHERE isBookmarked = 1 ORDER BY timestamp DESC")
    suspend fun getBookmarkedConversations(): List<Conversation>
    
    @Insert
    suspend fun insertConversation(conversation: Conversation)
    
    @Update
    suspend fun updateConversation(conversation: Conversation)
    
    @Delete
    suspend fun deleteConversation(conversation: Conversation)
    
    @Query("DELETE FROM conversations WHERE timestamp < :timestamp AND isBookmarked = 0")
    suspend fun deleteOldConversations(timestamp: Long)
}

@Dao
interface UserPreferencesDao {
    @Query("SELECT * FROM user_preferences WHERE id = 1")
    suspend fun getUserPreferences(): UserPreferences?
    
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertUserPreferences(preferences: UserPreferences)
    
    @Update
    suspend fun updateUserPreferences(preferences: UserPreferences)
} 