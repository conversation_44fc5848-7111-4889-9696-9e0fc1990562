package com.arina.ai.data.repository

import com.arina.ai.data.api.A4FRequest
import com.arina.ai.data.api.A4FResponse
import com.arina.ai.data.api.A4FService
import com.arina.ai.data.database.ArinaDatabase
import com.arina.ai.data.database.Conversation
import com.arina.ai.data.database.UserPreferences
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.flow.flowOn
import kotlinx.coroutines.withContext
import java.util.*

class ArinaRepository(
    private val database: ArinaDatabase,
    private val apiService: A4FService
) {
    // API Operations
    suspend fun generateResponse(
        prompt: String,
        model: String,
        emotionalState: String? = null,
        contextHistory: List<String>? = null
    ): Result<A4FResponse> = withContext(Dispatchers.IO) {
        try {
            val request = A4FRequest(
                prompt = prompt,
                model = model,
                emotionalState = emotionalState,
                contextHistory = contextHistory
            )
            
            val response = apiService.generateResponse(
                apiKey = getUserPreferences()?.selectedModel ?: "",
                request = request
            )
            
            if (response.isSuccessful && response.body() != null) {
                // Store conversation in database
                val conversation = Conversation(
                    userMessage = prompt,
                    arinaResponse = response.body()!!.response,
                    emotionalState = response.body()!!.emotionalState
                )
                database.conversationDao().insertConversation(conversation)
                
                Result.success(response.body()!!)
            } else {
                Result.failure(Exception("API call failed: ${response.code()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    suspend fun getAvailableModels(): Result<List<String>> = withContext(Dispatchers.IO) {
        try {
            val response = apiService.getAvailableModels(
                apiKey = getUserPreferences()?.selectedModel ?: ""
            )
            
            if (response.isSuccessful && response.body() != null) {
                Result.success(response.body()!!)
            } else {
                Result.failure(Exception("Failed to get models: ${response.code()}"))
            }
        } catch (e: Exception) {
            Result.failure(e)
        }
    }

    // Database Operations
    suspend fun getUserPreferences(): UserPreferences? = withContext(Dispatchers.IO) {
        database.userPreferencesDao().getUserPreferences()
    }

    suspend fun updateUserPreferences(preferences: UserPreferences) = withContext(Dispatchers.IO) {
        database.userPreferencesDao().updateUserPreferences(preferences)
    }

    fun getConversationHistory(): Flow<List<Conversation>> = flow {
        val conversations = database.conversationDao().getAllConversations()
        emit(conversations)
    }.flowOn(Dispatchers.IO)

    fun getBookmarkedConversations(): Flow<List<Conversation>> = flow {
        val conversations = database.conversationDao().getBookmarkedConversations()
        emit(conversations)
    }.flowOn(Dispatchers.IO)

    suspend fun bookmarkConversation(conversation: Conversation) = withContext(Dispatchers.IO) {
        database.conversationDao().updateConversation(
            conversation.copy(isBookmarked = true)
        )
    }

    suspend fun deleteConversation(conversation: Conversation) = withContext(Dispatchers.IO) {
        database.conversationDao().deleteConversation(conversation)
    }

    suspend fun cleanupOldConversations(maxAgeInMillis: Long) = withContext(Dispatchers.IO) {
        val cutoffTime = System.currentTimeMillis() - maxAgeInMillis
        database.conversationDao().deleteOldConversations(cutoffTime)
    }
} 