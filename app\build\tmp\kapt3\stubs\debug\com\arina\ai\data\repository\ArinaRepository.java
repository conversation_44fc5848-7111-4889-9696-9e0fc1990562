package com.arina.ai.data.repository;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000X\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0010\t\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0003\n\u0002\u0010 \n\u0002\b\u0006\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0004\u0018\u00002\u00020\u0001B\u0015\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u00a2\u0006\u0002\u0010\u0006J\u0019\u0010\u0007\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u000bJ\u0019\u0010\f\u001a\u00020\b2\u0006\u0010\r\u001a\u00020\u000eH\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u000fJ\u0019\u0010\u0010\u001a\u00020\b2\u0006\u0010\t\u001a\u00020\nH\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u000bJP\u0010\u0011\u001a\b\u0012\u0004\u0012\u00020\u00130\u00122\u0006\u0010\u0014\u001a\u00020\u00152\u0006\u0010\u0016\u001a\u00020\u00152\n\b\u0002\u0010\u0017\u001a\u0004\u0018\u00010\u00152\u0010\b\u0002\u0010\u0018\u001a\n\u0012\u0004\u0012\u00020\u0015\u0018\u00010\u0019H\u0086@\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0000\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u001a\u0010\u001bJ(\u0010\u001c\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\u00150\u00190\u0012H\u0086@\u00f8\u0001\u0001\u00f8\u0001\u0002\u00f8\u0001\u0000\u00f8\u0001\u0000\u00a2\u0006\u0004\b\u001d\u0010\u001eJ\u0012\u0010\u001f\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\u00190 J\u0012\u0010!\u001a\u000e\u0012\n\u0012\b\u0012\u0004\u0012\u00020\n0\u00190 J\u0013\u0010\"\u001a\u0004\u0018\u00010#H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010\u001eJ\u0019\u0010$\u001a\u00020\b2\u0006\u0010%\u001a\u00020#H\u0086@\u00f8\u0001\u0000\u00a2\u0006\u0002\u0010&R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000\u0082\u0002\u000f\n\u0002\b\u0019\n\u0002\b!\n\u0005\b\u00a1\u001e0\u0001\u00a8\u0006\'"}, d2 = {"Lcom/arina/ai/data/repository/ArinaRepository;", "", "database", "Lcom/arina/ai/data/database/ArinaDatabase;", "apiService", "Lcom/arina/ai/data/api/A4FService;", "(Lcom/arina/ai/data/database/ArinaDatabase;Lcom/arina/ai/data/api/A4FService;)V", "bookmarkConversation", "", "conversation", "Lcom/arina/ai/data/database/Conversation;", "(Lcom/arina/ai/data/database/Conversation;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "cleanupOldConversations", "maxAgeInMillis", "", "(JLkotlin/coroutines/Continuation;)Ljava/lang/Object;", "deleteConversation", "generateResponse", "Lkotlin/Result;", "Lcom/arina/ai/data/api/A4FResponse;", "prompt", "", "model", "emotionalState", "contextHistory", "", "generateResponse-yxL6bBk", "(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/util/List;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getAvailableModels", "getAvailableModels-IoAF18A", "(Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "getBookmarkedConversations", "Lkotlinx/coroutines/flow/Flow;", "getConversationHistory", "getUserPreferences", "Lcom/arina/ai/data/database/UserPreferences;", "updateUserPreferences", "preferences", "(Lcom/arina/ai/data/database/UserPreferences;Lkotlin/coroutines/Continuation;)Ljava/lang/Object;", "app_debug"})
public final class ArinaRepository {
    @org.jetbrains.annotations.NotNull
    private final com.arina.ai.data.database.ArinaDatabase database = null;
    @org.jetbrains.annotations.NotNull
    private final com.arina.ai.data.api.A4FService apiService = null;
    
    public ArinaRepository(@org.jetbrains.annotations.NotNull
    com.arina.ai.data.database.ArinaDatabase database, @org.jetbrains.annotations.NotNull
    com.arina.ai.data.api.A4FService apiService) {
        super();
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object getUserPreferences(@org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super com.arina.ai.data.database.UserPreferences> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object updateUserPreferences(@org.jetbrains.annotations.NotNull
    com.arina.ai.data.database.UserPreferences preferences, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.arina.ai.data.database.Conversation>> getConversationHistory() {
        return null;
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.Flow<java.util.List<com.arina.ai.data.database.Conversation>> getBookmarkedConversations() {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object bookmarkConversation(@org.jetbrains.annotations.NotNull
    com.arina.ai.data.database.Conversation conversation, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object deleteConversation(@org.jetbrains.annotations.NotNull
    com.arina.ai.data.database.Conversation conversation, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
    
    @org.jetbrains.annotations.Nullable
    public final java.lang.Object cleanupOldConversations(long maxAgeInMillis, @org.jetbrains.annotations.NotNull
    kotlin.coroutines.Continuation<? super kotlin.Unit> $completion) {
        return null;
    }
}