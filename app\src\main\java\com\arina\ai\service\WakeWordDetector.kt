package com.arina.ai.service

import android.content.Context
import android.media.AudioFormat
import android.media.AudioRecord
import android.media.MediaRecorder
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch
import org.tensorflow.lite.Interpreter
import java.io.File
import java.nio.ByteBuffer
import java.nio.ByteOrder
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class WakeWordDetector @Inject constructor(
    @ApplicationContext private val context: Context
) {
    private var audioRecord: AudioRecord? = null
    private var interpreter: Interpreter? = null
    private var isDetecting = false
    private var detectionJob: Job? = null
    private val modelBuffer = ByteBuffer.allocateDirect(BUFFER_SIZE)
    
    companion object {
        private const val SAMPLE_RATE = 16000
        private const val CHANNEL_CONFIG = AudioFormat.CHANNEL_IN_MONO
        private const val AUDIO_FORMAT = AudioFormat.ENCODING_PCM_16BIT
        private const val BUFFER_SIZE = 2 * SAMPLE_RATE // 2 seconds buffer
    }

    init {
        initializeTensorFlow()
    }

    private fun initializeTensorFlow() {
        try {
            val modelFile = File(context.getExternalFilesDir(null), "wake_word_model.tflite")
            interpreter = Interpreter(modelFile)
            modelBuffer.order(ByteOrder.nativeOrder())
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    fun startDetection(onWakeWordDetected: (Boolean) -> Unit) {
        if (isDetecting) return

        isDetecting = true
        initializeAudioRecord()

        detectionJob = CoroutineScope(Dispatchers.Default).launch {
            val audioBuffer = ShortArray(BUFFER_SIZE / 2)
            var bytesRead: Int

            audioRecord?.startRecording()

            while (isDetecting) {
                bytesRead = audioRecord?.read(audioBuffer, 0, audioBuffer.size) ?: 0
                if (bytesRead > 0) {
                    if (processAudioBuffer(audioBuffer, bytesRead)) {
                        onWakeWordDetected(true)
                    }
                }
            }
        }
    }

    private fun initializeAudioRecord() {
        val minBufferSize = AudioRecord.getMinBufferSize(
            SAMPLE_RATE,
            CHANNEL_CONFIG,
            AUDIO_FORMAT
        )

        audioRecord = AudioRecord(
            MediaRecorder.AudioSource.MIC,
            SAMPLE_RATE,
            CHANNEL_CONFIG,
            AUDIO_FORMAT,
            minBufferSize
        )
    }

    private fun processAudioBuffer(buffer: ShortArray, size: Int): Boolean {
        modelBuffer.rewind()
        for (i in 0 until size) {
            modelBuffer.putShort(buffer[i])
        }

        val outputBuffer = ByteBuffer.allocateDirect(4) // For single float output
        outputBuffer.order(ByteOrder.nativeOrder())

        interpreter?.run(modelBuffer, outputBuffer)

        outputBuffer.rewind()
        val confidence = outputBuffer.float
        return confidence > 0.8f // Threshold for wake word detection
    }

    fun stopDetection() {
        isDetecting = false
        detectionJob?.cancel()
        audioRecord?.apply {
            stop()
            release()
        }
        audioRecord = null
    }

    fun release() {
        stopDetection()
        interpreter?.close()
        interpreter = null
    }
} 