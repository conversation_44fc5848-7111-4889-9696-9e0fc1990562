package com.arina.ai.ui.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u0014\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\bH\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002R\u001c\u0010\u0003\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0005\u0010\u0006R\u001c\u0010\b\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\t\u0010\u0006R\u001c\u0010\n\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u000b\u0010\u0006R\u001c\u0010\f\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\r\u0010\u0006R\u001c\u0010\u000e\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u000f\u0010\u0006R\u001c\u0010\u0010\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0011\u0010\u0006R\u001c\u0010\u0012\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0013\u0010\u0006R\u001c\u0010\u0014\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0015\u0010\u0006R\u001c\u0010\u0016\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0017\u0010\u0006R\u001c\u0010\u0018\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u0019\u0010\u0006R\u001c\u0010\u001a\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u001b\u0010\u0006R\u001c\u0010\u001c\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u001d\u0010\u0006R\u001c\u0010\u001e\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\u001f\u0010\u0006R\u001c\u0010 \u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b!\u0010\u0006R\u001c\u0010\"\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b#\u0010\u0006R\u001c\u0010$\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b%\u0010\u0006R\u001c\u0010&\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b\'\u0010\u0006R\u001c\u0010(\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b)\u0010\u0006R\u001c\u0010*\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b+\u0010\u0006R\u001c\u0010,\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b-\u0010\u0006R\u001c\u0010.\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b/\u0010\u0006R\u001c\u00100\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b1\u0010\u0006R\u001c\u00102\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b3\u0010\u0006R\u001c\u00104\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b5\u0010\u0006R\u001c\u00106\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b7\u0010\u0006R\u001c\u00108\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b9\u0010\u0006R\u001c\u0010:\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b;\u0010\u0006R\u001c\u0010<\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b=\u0010\u0006R\u001c\u0010>\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\b?\u0010\u0006R\u001c\u0010@\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\bA\u0010\u0006R\u001c\u0010B\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\bC\u0010\u0006R\u001c\u0010D\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\bE\u0010\u0006R\u001c\u0010F\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\bG\u0010\u0006R\u001c\u0010H\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\bI\u0010\u0006R\u001c\u0010J\u001a\u00020\u0004\u00f8\u0001\u0000\u00f8\u0001\u0001\u00f8\u0001\u0002\u00a2\u0006\n\n\u0002\u0010\u0007\u001a\u0004\bK\u0010\u0006\u0082\u0002\u000f\n\u0002\b\u0019\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b!\u00a8\u0006L"}, d2 = {"Lcom/arina/ai/ui/theme/ArinaColors;", "", "()V", "BorderAccent", "Landroidx/compose/ui/graphics/Color;", "getBorderAccent-0d7_KjU", "()J", "J", "BorderPrimary", "getBorderPrimary-0d7_KjU", "BorderSecondary", "getBorderSecondary-0d7_KjU", "CyanGlow", "getCyanGlow-0d7_KjU", "DarkGray", "getDarkGray-0d7_KjU", "DeepBlack", "getDeepBlack-0d7_KjU", "Error", "getError-0d7_KjU", "GradientEnd", "getGradientEnd-0d7_KjU", "GradientMid", "getGradientMid-0d7_KjU", "GradientStart", "getGradientStart-0d7_KjU", "Info", "getInfo-0d7_KjU", "MagentaGlow", "getMagentaGlow-0d7_KjU", "MidnightBlue", "getMidnightBlue-0d7_KjU", "NeonBlue", "getNeonBlue-0d7_KjU", "NeonCyan", "getNeonCyan-0d7_KjU", "NeonGreen", "getNeonGreen-0d7_KjU", "NeonMagenta", "getNeonMagenta-0d7_KjU", "NeonOrange", "getNeonOrange-0d7_KjU", "NeonPurple", "getNeonPurple-0d7_KjU", "NeonRed", "getNeonRed-0d7_KjU", "NeonYellow", "getNeonYellow-0d7_KjU", "OverlayDark", "getOverlayDark-0d7_KjU", "OverlayLight", "getOverlayLight-0d7_KjU", "OverlayMedium", "getOverlayMedium-0d7_KjU", "SpaceBlack", "getSpaceBlack-0d7_KjU", "Success", "getSuccess-0d7_KjU", "SurfaceAccent", "getSurfaceAccent-0d7_KjU", "SurfaceInteractive", "getSurfaceInteractive-0d7_KjU", "SurfacePrimary", "getSurfacePrimary-0d7_KjU", "SurfaceSecondary", "getSurfaceSecondary-0d7_KjU", "TextDisabled", "getTextDisabled-0d7_KjU", "TextPrimary", "getTextPrimary-0d7_KjU", "TextSecondary", "getTextSecondary-0d7_KjU", "VioletGlow", "getVioletGlow-0d7_KjU", "Warning", "getWarning-0d7_KjU", "app_debug"})
public final class ArinaColors {
    private static final long NeonCyan = 0L;
    private static final long NeonMagenta = 0L;
    private static final long NeonYellow = 0L;
    private static final long NeonBlue = 0L;
    private static final long NeonPurple = 0L;
    private static final long NeonOrange = 0L;
    private static final long NeonRed = 0L;
    private static final long NeonGreen = 0L;
    private static final long DeepBlack = 0L;
    private static final long DarkGray = 0L;
    private static final long MidnightBlue = 0L;
    private static final long SpaceBlack = 0L;
    private static final long TextPrimary = 0L;
    private static final long TextSecondary = 0L;
    private static final long TextDisabled = 0L;
    private static final long CyanGlow = 0L;
    private static final long MagentaGlow = 0L;
    private static final long VioletGlow = 0L;
    private static final long GradientStart = 0L;
    private static final long GradientMid = 0L;
    private static final long GradientEnd = 0L;
    private static final long Success = 0L;
    private static final long Warning = 0L;
    private static final long Error = 0L;
    private static final long Info = 0L;
    private static final long SurfacePrimary = 0L;
    private static final long SurfaceSecondary = 0L;
    private static final long SurfaceAccent = 0L;
    private static final long SurfaceInteractive = 0L;
    private static final long BorderPrimary = 0L;
    private static final long BorderSecondary = 0L;
    private static final long BorderAccent = 0L;
    private static final long OverlayLight = 0L;
    private static final long OverlayMedium = 0L;
    private static final long OverlayDark = 0L;
    @org.jetbrains.annotations.NotNull
    public static final com.arina.ai.ui.theme.ArinaColors INSTANCE = null;
    
    private ArinaColors() {
        super();
    }
}