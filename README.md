# Arina - AI Voice Assistant 🤖✨

Arina is a stunning Android AI assistant app built with **Kotlin**, **Jetpack Compose**, and **MVVM architecture**. <PERSON>na features beautiful neon glow effects, tap-to-listen voice interaction, and A4F API integration for accessing multiple AI models.

## 🌟 Features

### Core Functionality
- **🎤 Tap-to-Listen**: Simple tap interface for voice commands (no wake word)
- **🗣️ Text-to-Speech**: Natural voice responses with emotional inflection
- **🧠 A4F API Integration**: Access to GPT-4, Claude, Gemini, and more AI models
- **🔄 Smart Fallbacks**: Automatic model switching for reliability
- **🔒 Secure Storage**: Encrypted API key storage

### Stunning Neon UI
- **✨ Neon Glow Effects**: Custom neon pink, blue, violet, and cyan glows
- **🌊 Voice Animations**: Real-time voice wave visualizations
- **🎨 Custom Components**: No Material Design - fully custom Arina aesthetic
- **🌙 Dark Theme**: Black background with vibrant neon accents
- **💫 Smooth Animations**: Pulsing effects and smooth transitions

### Smart AI Features
- **💬 Conversation Context**: Maintains chat history for natural flow
- **🎭 Emotional Responses**: <PERSON><PERSON> responds with warmth and personality
- **⚡ Multiple Models**: Access to latest GPT, Claude, and Gemini models

## Technical Details

### Architecture
- **UI**: Jetpack Compose (without Material3)
- **State Management**: ViewModel + StateFlow
- **Data Persistence**: DataStore
- **Networking**: Retrofit
- **Wake Word Detection**: Porcupine SDK

### Project Structure
```
/app/src/main/java/com/nova/engine/
├── data/
│   └── datastore/         # DataStore preferences repository
├── network/
│   └── a4f/               # A4F API service and repository
├── ui/
│   ├── common/            # Common UI components
│   ├── setup/             # Setup screens
│   └── main/              # Main app screen
├── wakeword/              # Wake word detection
└── NovaApplication.kt     # Application class
```

## Requirements
- Android Studio Flamingo or newer
- Gradle 8.2
- Kotlin 1.9
- Android SDK 24+

## Setup
1. Clone the repository
2. Open the project in Android Studio
3. Build and run the app

## Dependencies
- Jetpack Compose
- Retrofit
- DataStore
- Porcupine SDK
- Accompanist Permissions

## License
This project is proprietary and confidential. 