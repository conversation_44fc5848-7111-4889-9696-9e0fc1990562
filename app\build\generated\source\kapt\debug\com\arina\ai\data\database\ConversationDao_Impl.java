package com.arina.ai.data.database;

import android.database.Cursor;
import android.os.CancellationSignal;
import androidx.annotation.NonNull;
import androidx.room.CoroutinesRoom;
import androidx.room.EntityDeletionOrUpdateAdapter;
import androidx.room.EntityInsertionAdapter;
import androidx.room.RoomDatabase;
import androidx.room.RoomSQLiteQuery;
import androidx.room.SharedSQLiteStatement;
import androidx.room.util.CursorUtil;
import androidx.room.util.DBUtil;
import androidx.sqlite.db.SupportSQLiteStatement;
import java.lang.Class;
import java.lang.Exception;
import java.lang.Object;
import java.lang.Override;
import java.lang.String;
import java.lang.SuppressWarnings;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Callable;
import javax.annotation.processing.Generated;
import kotlin.Unit;
import kotlin.coroutines.Continuation;

@Generated("androidx.room.RoomProcessor")
@SuppressWarnings({"unchecked", "deprecation"})
public final class ConversationDao_Impl implements ConversationDao {
  private final RoomDatabase __db;

  private final EntityInsertionAdapter<Conversation> __insertionAdapterOfConversation;

  private final EntityDeletionOrUpdateAdapter<Conversation> __deletionAdapterOfConversation;

  private final EntityDeletionOrUpdateAdapter<Conversation> __updateAdapterOfConversation;

  private final SharedSQLiteStatement __preparedStmtOfDeleteOldConversations;

  public ConversationDao_Impl(@NonNull final RoomDatabase __db) {
    this.__db = __db;
    this.__insertionAdapterOfConversation = new EntityInsertionAdapter<Conversation>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "INSERT OR ABORT INTO `conversations` (`id`,`timestamp`,`userMessage`,`arinaResponse`,`emotionalState`,`importance`,`isBookmarked`) VALUES (?,?,?,?,?,?,?)";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Conversation entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        statement.bindLong(2, entity.getTimestamp());
        if (entity.getUserMessage() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getUserMessage());
        }
        if (entity.getArinaResponse() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getArinaResponse());
        }
        if (entity.getEmotionalState() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getEmotionalState());
        }
        statement.bindLong(6, entity.getImportance());
        final int _tmp = entity.isBookmarked() ? 1 : 0;
        statement.bindLong(7, _tmp);
      }
    };
    this.__deletionAdapterOfConversation = new EntityDeletionOrUpdateAdapter<Conversation>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "DELETE FROM `conversations` WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Conversation entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
      }
    };
    this.__updateAdapterOfConversation = new EntityDeletionOrUpdateAdapter<Conversation>(__db) {
      @Override
      @NonNull
      protected String createQuery() {
        return "UPDATE OR ABORT `conversations` SET `id` = ?,`timestamp` = ?,`userMessage` = ?,`arinaResponse` = ?,`emotionalState` = ?,`importance` = ?,`isBookmarked` = ? WHERE `id` = ?";
      }

      @Override
      protected void bind(@NonNull final SupportSQLiteStatement statement,
          @NonNull final Conversation entity) {
        if (entity.getId() == null) {
          statement.bindNull(1);
        } else {
          statement.bindString(1, entity.getId());
        }
        statement.bindLong(2, entity.getTimestamp());
        if (entity.getUserMessage() == null) {
          statement.bindNull(3);
        } else {
          statement.bindString(3, entity.getUserMessage());
        }
        if (entity.getArinaResponse() == null) {
          statement.bindNull(4);
        } else {
          statement.bindString(4, entity.getArinaResponse());
        }
        if (entity.getEmotionalState() == null) {
          statement.bindNull(5);
        } else {
          statement.bindString(5, entity.getEmotionalState());
        }
        statement.bindLong(6, entity.getImportance());
        final int _tmp = entity.isBookmarked() ? 1 : 0;
        statement.bindLong(7, _tmp);
        if (entity.getId() == null) {
          statement.bindNull(8);
        } else {
          statement.bindString(8, entity.getId());
        }
      }
    };
    this.__preparedStmtOfDeleteOldConversations = new SharedSQLiteStatement(__db) {
      @Override
      @NonNull
      public String createQuery() {
        final String _query = "DELETE FROM conversations WHERE timestamp < ? AND isBookmarked = 0";
        return _query;
      }
    };
  }

  @Override
  public Object insertConversation(final Conversation conversation,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __insertionAdapterOfConversation.insert(conversation);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteConversation(final Conversation conversation,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __deletionAdapterOfConversation.handle(conversation);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object updateConversation(final Conversation conversation,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        __db.beginTransaction();
        try {
          __updateAdapterOfConversation.handle(conversation);
          __db.setTransactionSuccessful();
          return Unit.INSTANCE;
        } finally {
          __db.endTransaction();
        }
      }
    }, $completion);
  }

  @Override
  public Object deleteOldConversations(final long timestamp,
      final Continuation<? super Unit> $completion) {
    return CoroutinesRoom.execute(__db, true, new Callable<Unit>() {
      @Override
      @NonNull
      public Unit call() throws Exception {
        final SupportSQLiteStatement _stmt = __preparedStmtOfDeleteOldConversations.acquire();
        int _argIndex = 1;
        _stmt.bindLong(_argIndex, timestamp);
        try {
          __db.beginTransaction();
          try {
            _stmt.executeUpdateDelete();
            __db.setTransactionSuccessful();
            return Unit.INSTANCE;
          } finally {
            __db.endTransaction();
          }
        } finally {
          __preparedStmtOfDeleteOldConversations.release(_stmt);
        }
      }
    }, $completion);
  }

  @Override
  public Object getAllConversations(final Continuation<? super List<Conversation>> $completion) {
    final String _sql = "SELECT * FROM conversations ORDER BY timestamp DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<Conversation>>() {
      @Override
      @NonNull
      public List<Conversation> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfUserMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "userMessage");
          final int _cursorIndexOfArinaResponse = CursorUtil.getColumnIndexOrThrow(_cursor, "arinaResponse");
          final int _cursorIndexOfEmotionalState = CursorUtil.getColumnIndexOrThrow(_cursor, "emotionalState");
          final int _cursorIndexOfImportance = CursorUtil.getColumnIndexOrThrow(_cursor, "importance");
          final int _cursorIndexOfIsBookmarked = CursorUtil.getColumnIndexOrThrow(_cursor, "isBookmarked");
          final List<Conversation> _result = new ArrayList<Conversation>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Conversation _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final long _tmpTimestamp;
            _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
            final String _tmpUserMessage;
            if (_cursor.isNull(_cursorIndexOfUserMessage)) {
              _tmpUserMessage = null;
            } else {
              _tmpUserMessage = _cursor.getString(_cursorIndexOfUserMessage);
            }
            final String _tmpArinaResponse;
            if (_cursor.isNull(_cursorIndexOfArinaResponse)) {
              _tmpArinaResponse = null;
            } else {
              _tmpArinaResponse = _cursor.getString(_cursorIndexOfArinaResponse);
            }
            final String _tmpEmotionalState;
            if (_cursor.isNull(_cursorIndexOfEmotionalState)) {
              _tmpEmotionalState = null;
            } else {
              _tmpEmotionalState = _cursor.getString(_cursorIndexOfEmotionalState);
            }
            final int _tmpImportance;
            _tmpImportance = _cursor.getInt(_cursorIndexOfImportance);
            final boolean _tmpIsBookmarked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsBookmarked);
            _tmpIsBookmarked = _tmp != 0;
            _item = new Conversation(_tmpId,_tmpTimestamp,_tmpUserMessage,_tmpArinaResponse,_tmpEmotionalState,_tmpImportance,_tmpIsBookmarked);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @Override
  public Object getBookmarkedConversations(
      final Continuation<? super List<Conversation>> $completion) {
    final String _sql = "SELECT * FROM conversations WHERE isBookmarked = 1 ORDER BY timestamp DESC";
    final RoomSQLiteQuery _statement = RoomSQLiteQuery.acquire(_sql, 0);
    final CancellationSignal _cancellationSignal = DBUtil.createCancellationSignal();
    return CoroutinesRoom.execute(__db, false, _cancellationSignal, new Callable<List<Conversation>>() {
      @Override
      @NonNull
      public List<Conversation> call() throws Exception {
        final Cursor _cursor = DBUtil.query(__db, _statement, false, null);
        try {
          final int _cursorIndexOfId = CursorUtil.getColumnIndexOrThrow(_cursor, "id");
          final int _cursorIndexOfTimestamp = CursorUtil.getColumnIndexOrThrow(_cursor, "timestamp");
          final int _cursorIndexOfUserMessage = CursorUtil.getColumnIndexOrThrow(_cursor, "userMessage");
          final int _cursorIndexOfArinaResponse = CursorUtil.getColumnIndexOrThrow(_cursor, "arinaResponse");
          final int _cursorIndexOfEmotionalState = CursorUtil.getColumnIndexOrThrow(_cursor, "emotionalState");
          final int _cursorIndexOfImportance = CursorUtil.getColumnIndexOrThrow(_cursor, "importance");
          final int _cursorIndexOfIsBookmarked = CursorUtil.getColumnIndexOrThrow(_cursor, "isBookmarked");
          final List<Conversation> _result = new ArrayList<Conversation>(_cursor.getCount());
          while (_cursor.moveToNext()) {
            final Conversation _item;
            final String _tmpId;
            if (_cursor.isNull(_cursorIndexOfId)) {
              _tmpId = null;
            } else {
              _tmpId = _cursor.getString(_cursorIndexOfId);
            }
            final long _tmpTimestamp;
            _tmpTimestamp = _cursor.getLong(_cursorIndexOfTimestamp);
            final String _tmpUserMessage;
            if (_cursor.isNull(_cursorIndexOfUserMessage)) {
              _tmpUserMessage = null;
            } else {
              _tmpUserMessage = _cursor.getString(_cursorIndexOfUserMessage);
            }
            final String _tmpArinaResponse;
            if (_cursor.isNull(_cursorIndexOfArinaResponse)) {
              _tmpArinaResponse = null;
            } else {
              _tmpArinaResponse = _cursor.getString(_cursorIndexOfArinaResponse);
            }
            final String _tmpEmotionalState;
            if (_cursor.isNull(_cursorIndexOfEmotionalState)) {
              _tmpEmotionalState = null;
            } else {
              _tmpEmotionalState = _cursor.getString(_cursorIndexOfEmotionalState);
            }
            final int _tmpImportance;
            _tmpImportance = _cursor.getInt(_cursorIndexOfImportance);
            final boolean _tmpIsBookmarked;
            final int _tmp;
            _tmp = _cursor.getInt(_cursorIndexOfIsBookmarked);
            _tmpIsBookmarked = _tmp != 0;
            _item = new Conversation(_tmpId,_tmpTimestamp,_tmpUserMessage,_tmpArinaResponse,_tmpEmotionalState,_tmpImportance,_tmpIsBookmarked);
            _result.add(_item);
          }
          return _result;
        } finally {
          _cursor.close();
          _statement.release();
        }
      }
    }, $completion);
  }

  @NonNull
  public static List<Class<?>> getRequiredConverters() {
    return Collections.emptyList();
  }
}
