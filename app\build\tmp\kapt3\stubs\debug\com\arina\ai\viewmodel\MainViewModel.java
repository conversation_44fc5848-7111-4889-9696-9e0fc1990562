package com.arina.ai.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000p\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0003\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0004\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0003\n\u0002\u0010\u000e\n\u0002\b\t\n\u0002\u0018\u0002\n\u0000\u0018\u00002\u00020\u0001B\u001d\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u0012\u0006\u0010\u0004\u001a\u00020\u0005\u0012\u0006\u0010\u0006\u001a\u00020\u0007\u00a2\u0006\u0002\u0010\bJ\u0010\u0010\u0012\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0015H\u0002J\b\u0010\u0016\u001a\u00020\u0013H\u0002J\u0010\u0010\u0017\u001a\u00020\u00132\u0006\u0010\u0014\u001a\u00020\u0015H\u0002J\u0010\u0010\u0018\u001a\u00020\u00132\u0006\u0010\u0019\u001a\u00020\u001aH\u0002J\u000e\u0010\u001b\u001a\u00020\u00132\u0006\u0010\u001c\u001a\u00020\u001dJ\u0018\u0010\u001e\u001a\u00020\u00132\u0006\u0010\u001f\u001a\u00020 2\u0006\u0010!\u001a\u00020\"H\u0002J\b\u0010#\u001a\u00020\u0013H\u0014J\u0018\u0010$\u001a\u00020\u00132\u0006\u0010%\u001a\u00020&2\u0006\u0010\'\u001a\u00020\"H\u0002J\b\u0010(\u001a\u00020\u0013H\u0002J\u000e\u0010)\u001a\u00020\u00132\u0006\u0010*\u001a\u00020\u0011J\u0010\u0010+\u001a\u00020\u00132\u0006\u0010,\u001a\u00020\"H\u0002J\b\u0010-\u001a\u00020\u0013H\u0002J\u0010\u0010.\u001a\u00020\u00132\u0006\u0010/\u001a\u000200H\u0002R\u0014\u0010\t\u001a\b\u0012\u0004\u0012\u00020\u000b0\nX\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0002\u001a\u00020\u0003X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0006\u001a\u00020\u0007X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u000e\u0010\u0004\u001a\u00020\u0005X\u0082\u0004\u00a2\u0006\u0002\n\u0000R\u0017\u0010\f\u001a\b\u0012\u0004\u0012\u00020\u000b0\r\u00a2\u0006\b\n\u0000\u001a\u0004\b\u000e\u0010\u000fR\u0010\u0010\u0010\u001a\u0004\u0018\u00010\u0011X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u00061"}, d2 = {"Lcom/arina/ai/viewmodel/MainViewModel;", "Landroidx/lifecycle/ViewModel;", "repository", "Lcom/arina/ai/data/repository/ArinaRepository;", "textToSpeech", "Landroid/speech/tts/TextToSpeech;", "systemControlService", "Lcom/arina/ai/service/SystemControlService;", "(Lcom/arina/ai/data/repository/ArinaRepository;Landroid/speech/tts/TextToSpeech;Lcom/arina/ai/service/SystemControlService;)V", "_viewState", "Lkotlinx/coroutines/flow/MutableStateFlow;", "Lcom/arina/ai/viewmodel/MainViewState;", "viewState", "Lkotlinx/coroutines/flow/StateFlow;", "getViewState", "()Lkotlinx/coroutines/flow/StateFlow;", "voiceInteractionService", "Lcom/arina/ai/service/VoiceInteractionService;", "bookmarkConversation", "", "conversation", "Lcom/arina/ai/data/database/Conversation;", "clearError", "deleteConversation", "executeSystemCommand", "command", "Lcom/arina/ai/viewmodel/SystemCommand;", "handleEvent", "event", "Lcom/arina/ai/viewmodel/MainViewEvent;", "handleSuccessfulResponse", "response", "Lcom/arina/ai/data/api/A4FResponse;", "speakResponse", "", "onCleared", "processUserInput", "text", "", "isVoice", "refreshSystemInfo", "setVoiceInteractionService", "service", "startListening", "wakeWordDetected", "stopListening", "updatePreferences", "preferences", "Lcom/arina/ai/data/database/UserPreferences;", "app_debug"})
public final class MainViewModel extends androidx.lifecycle.ViewModel {
    @org.jetbrains.annotations.NotNull
    private final com.arina.ai.data.repository.ArinaRepository repository = null;
    @org.jetbrains.annotations.NotNull
    private final android.speech.tts.TextToSpeech textToSpeech = null;
    @org.jetbrains.annotations.NotNull
    private final com.arina.ai.service.SystemControlService systemControlService = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.MutableStateFlow<com.arina.ai.viewmodel.MainViewState> _viewState = null;
    @org.jetbrains.annotations.NotNull
    private final kotlinx.coroutines.flow.StateFlow<com.arina.ai.viewmodel.MainViewState> viewState = null;
    @org.jetbrains.annotations.Nullable
    private com.arina.ai.service.VoiceInteractionService voiceInteractionService;
    
    public MainViewModel(@org.jetbrains.annotations.NotNull
    com.arina.ai.data.repository.ArinaRepository repository, @org.jetbrains.annotations.NotNull
    android.speech.tts.TextToSpeech textToSpeech, @org.jetbrains.annotations.NotNull
    com.arina.ai.service.SystemControlService systemControlService) {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public final kotlinx.coroutines.flow.StateFlow<com.arina.ai.viewmodel.MainViewState> getViewState() {
        return null;
    }
    
    public final void setVoiceInteractionService(@org.jetbrains.annotations.NotNull
    com.arina.ai.service.VoiceInteractionService service) {
    }
    
    public final void handleEvent(@org.jetbrains.annotations.NotNull
    com.arina.ai.viewmodel.MainViewEvent event) {
    }
    
    private final void startListening(boolean wakeWordDetected) {
    }
    
    private final void stopListening() {
    }
    
    private final void processUserInput(java.lang.String text, boolean isVoice) {
    }
    
    private final void handleSuccessfulResponse(com.arina.ai.data.api.A4FResponse response, boolean speakResponse) {
    }
    
    private final void bookmarkConversation(com.arina.ai.data.database.Conversation conversation) {
    }
    
    private final void deleteConversation(com.arina.ai.data.database.Conversation conversation) {
    }
    
    private final void updatePreferences(com.arina.ai.data.database.UserPreferences preferences) {
    }
    
    private final void executeSystemCommand(com.arina.ai.viewmodel.SystemCommand command) {
    }
    
    private final void refreshSystemInfo() {
    }
    
    private final void clearError() {
    }
    
    @java.lang.Override
    protected void onCleared() {
    }
}