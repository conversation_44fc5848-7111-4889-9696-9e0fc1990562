package com.arina.ai.ui.theme;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001c\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010\u0007\n\u0002\b\u0003\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J%\u0010\u0003\u001a\u00020\u00042\u0006\u0010\u0005\u001a\u00020\u00042\b\b\u0002\u0010\u0006\u001a\u00020\u0007\u00f8\u0001\u0000\u00f8\u0001\u0001\u00a2\u0006\u0004\b\b\u0010\t\u0082\u0002\u000b\n\u0005\b\u00a1\u001e0\u0001\n\u0002\b\u0019\u00a8\u0006\n"}, d2 = {"Lcom/arina/ai/ui/theme/ArinaThemeUtils;", "", "()V", "createNeonGlow", "Landroidx/compose/ui/graphics/Color;", "color", "alpha", "", "createNeonGlow-5vOe2sY", "(JF)J", "app_debug"})
public final class ArinaThemeUtils {
    @org.jetbrains.annotations.NotNull
    public static final com.arina.ai.ui.theme.ArinaThemeUtils INSTANCE = null;
    
    private ArinaThemeUtils() {
        super();
    }
}