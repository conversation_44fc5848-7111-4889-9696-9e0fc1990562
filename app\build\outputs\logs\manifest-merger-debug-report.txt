-- Merging decision tree log ---
manifest
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:2:1-108:12
INJECTED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:2:1-108:12
INJECTED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:2:1-108:12
INJECTED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:2:1-108:12
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9515e7d883f7071e783a52c22d69d92c\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.airbnb.android:lottie-compose:6.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f5ff6c81dc49d2b14a73be2de8c3471\transformed\lottie-compose-6.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\caaa22807b29cbcd058e6ca17cad94f2\transformed\material3-1.1.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\14e7cc55df6c4f938c4ba7de31cc1a0d\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\transforms-3\10c3370dba421968a476cecec6b43d26\transformed\hilt-android-2.48\AndroidManifest.xml:16:1-19:12
MERGED from [androidx.navigation:navigation-compose:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\091b89392eebd1983c97270a3ea2e88f\transformed\navigation-compose-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\fac6776b119677f70c2737cb6fc008bf\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\7cf805e6d5eaee3aeed98150e2b50a46\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\bfb81b6df8a5cc8af0dbcbd7097efa34\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\67391349161bb7d9374d84a825ccc15e\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\54dba944c0e1b7098d0316d302cbb815\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\5e1624fbcbcbad53e75668d2c58183a2\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2dda48ec20754d7a266c9cf078f0d0fc\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\066c60f300013595aebf0ac49332b168\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\5aa7c16010929e5a34dceedb38be4f70\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\fb4afee682f3a03489d458264180696a\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\622d3c1d9a9fdbac4ffcf388c3880994\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d4e3f78e660857ffd99f26367e80f279\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\c4d0d11e2ae48d711635c1b7b664ae4d\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0efd23bbd0106786d0ccaeef17d4176e\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\9bcbc9f8172b6c83a87505e5f846139f\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d9b68a39eaa82fe8f489e3031923adb9\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2705adae8bd7b86a2eeefb93e6d40613\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:17:1-28:12
MERGED from [com.airbnb.android:lottie:6.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\be4d7956f63fad6a2908a9518ac09c51\transformed\lottie-6.3.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\08b9faa45c5837e0e1fbae153d52e70d\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\895477fa5033432c05b79dc01cd2b72e\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\358e273e9a2417ed24f02c311ea2ead3\transformed\fragment-1.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\e048f096732ed3286ecb33d88bdc8bcb\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\13da72d9b6e6e44a6adeadf5ba946485\transformed\navigation-runtime-ktx-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\06a32f20c9dddf4134df46d381c57bac\transformed\navigation-runtime-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\09f534280917575e992443df7e3e43f8\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\b0f1f905e2292d255f0c59ce6b514092\transformed\activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0d089874ae0a123dfa63cd7e293a8d4e\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\cf9863b96e0fa0e3f2e2d7e5afdcba15\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-livedata:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\69c5da8aabb8aa995ca7b4b522487c89\transformed\runtime-livedata-1.5.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-common-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\b11b75990ac7ff378ec7eaf71211d7ff\transformed\navigation-common-ktx-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.navigation:navigation-common:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\8b0c54bce6cbc8cb8a26628ee2c9174e\transformed\navigation-common-2.5.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\321a77da541c7973e1faddbea63ff37d\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\222459b6241d56f6f6360aa54805a23e\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\2b3fcb89c78062811be9a3d33f24f19e\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\0ba9bdec507852338c756d6babae2709\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b50e821ea9d6b95ff5b074abff6c26d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd145e5bd99a4f9bf8ccb9784bd6518c\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2b730e4c27221a8631d292c0c9ce1ba5\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b1146cfbfb3862b0a164f1a02d85268\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\91c9d373dcb2d3e3f2672d9251a8af38\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa8b3d3e2faa2b54bbfdbc227ec2daf1\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8156e413c2861308ca8853e0b0288314\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\32e62848943abee22af881a758f90e83\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b15d10bbf71c8d5b5d51f157942f826\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\af93152fa8e6106f43095cf3a3c137bd\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9ed35dba98892a7f7ec3250fc97efe05\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0daabc13164700d8e959c998861fc536\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4ca3d52b3ba6b42ff17d04a85df62a61\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\18fdd20392207bb6cdbde35eebdbeb5f\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\031433624e52174c2636c2b5a0147ae4\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\237d6bc09fe36c575f671a01372b3988\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\e44b6092d301126dd6e126ccf393f4dd\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c659bea7b1d60839c0336fc95ad688cd\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a90e53643f840bb6fa861430ffcfb459\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\b5020a7b046d2f049b44c14ea4b7abda\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f445b7eee75118b9dc957b2fbd2da47\transformed\room-runtime-2.6.1\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6429a5d04179c1ac0746f4a3dce30db6\transformed\room-ktx-2.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\transforms-3\4ec6c1c6460e6f2d85fc2491f8f1a9bc\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:17:1-22:12
MERGED from [org.tensorflow:tensorflow-lite-support:0.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\09ef185c16eb09189ca62c9e87639751\transformed\tensorflow-lite-support-0.4.4\AndroidManifest.xml:2:1-5:12
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\525d51ef284cbc1414fa013ef97634c8\transformed\tensorflow-lite-2.14.0\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\040ec24f41c6e30600db53f228cce65b\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab27d6ee8f62fbac2f082d30755d9b99\transformed\sqlite-2.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\9aec26117782ca9d587d428657d5104a\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dccbf93a9877e5958cc2f239b9152d1d\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5113d6d5b69d4e02f7b7b08f78736cf\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\feff2dda9233c9b99fafd9316c62231a\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8d0f68a3cbcc70041c66c54f5b861c50\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e46feee4f62b62bf5961af7b461af3b\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\54ad1084872b6c88bcd746dbacdd625c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\transforms-3\8cce6ecce879efb1f440331208bfe511\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:16:1-19:12
MERGED from [com.google.android.gms:play-services-tasks:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\dacf7913942acb3ac1d7cd915189b937\transformed\play-services-tasks-16.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [org.tensorflow:tensorflow-lite-support-api:0.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\0aa40285f7452496e41138935403b60a\transformed\tensorflow-lite-support-api-0.4.4\AndroidManifest.xml:2:1-5:12
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9752deab1625ddc83634fcd528444f\transformed\tensorflow-lite-api-2.14.0\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.gms:play-services-basement:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\afd914afebd159dc02db3b680f36ef47\transformed\play-services-basement-16.0.1\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\059dd6d75ff728d64220d44f6ce9e737\transformed\image-1.0.0-beta1\AndroidManifest.xml:2:1-9:12
	package
		INJECTED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:6:5-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:7:5-79
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.RECORD_AUDIO
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:10:5-71
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:10:22-68
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:11:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:11:22-77
uses-permission#android.permission.WRITE_SETTINGS
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:14:5-15:47
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:15:9-44
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:14:22-70
uses-permission#android.permission.WRITE_SECURE_SETTINGS
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:16:5-17:47
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:17:9-44
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:16:22-77
uses-permission#android.permission.DEVICE_POWER
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:18:5-19:47
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:19:9-44
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:18:22-68
uses-permission#android.permission.CALL_PHONE
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:22:5-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:22:22-66
uses-permission#android.permission.READ_PHONE_STATE
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:23:5-75
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:23:22-72
uses-permission#android.permission.ANSWER_PHONE_CALLS
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:24:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:24:22-74
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:27:5-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:27:22-74
uses-permission#android.permission.BIND_NOTIFICATION_LISTENER_SERVICE
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:28:5-29:47
	tools:ignore
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:29:9-44
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:28:22-90
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:32:5-80
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:32:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:33:5-81
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:33:22-78
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:36:5-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:36:22-75
uses-permission#android.permission.BLUETOOTH
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:39:5-68
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:39:22-65
uses-permission#android.permission.BLUETOOTH_ADMIN
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:40:5-74
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:40:22-71
uses-permission#android.permission.BLUETOOTH_CONNECT
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:41:5-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:41:22-73
application
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:43:5-107:19
INJECTED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:43:5-107:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0efd23bbd0106786d0ccaeef17d4176e\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0efd23bbd0106786d0ccaeef17d4176e\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2705adae8bd7b86a2eeefb93e6d40613\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2705adae8bd7b86a2eeefb93e6d40613\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:22:5-26:19
MERGED from [com.airbnb.android:lottie:6.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\be4d7956f63fad6a2908a9518ac09c51\transformed\lottie-6.3.0\AndroidManifest.xml:7:5-20
MERGED from [com.airbnb.android:lottie:6.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\be4d7956f63fad6a2908a9518ac09c51\transformed\lottie-6.3.0\AndroidManifest.xml:7:5-20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\0ba9bdec507852338c756d6babae2709\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\0ba9bdec507852338c756d6babae2709\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b50e821ea9d6b95ff5b074abff6c26d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b50e821ea9d6b95ff5b074abff6c26d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\e44b6092d301126dd6e126ccf393f4dd\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\e44b6092d301126dd6e126ccf393f4dd\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f445b7eee75118b9dc957b2fbd2da47\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f445b7eee75118b9dc957b2fbd2da47\transformed\room-runtime-2.6.1\AndroidManifest.xml:23:5-29:19
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\525d51ef284cbc1414fa013ef97634c8\transformed\tensorflow-lite-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\525d51ef284cbc1414fa013ef97634c8\transformed\tensorflow-lite-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5113d6d5b69d4e02f7b7b08f78736cf\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5113d6d5b69d4e02f7b7b08f78736cf\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8d0f68a3cbcc70041c66c54f5b861c50\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8d0f68a3cbcc70041c66c54f5b861c50\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.google.android.gms:play-services-tasks:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\dacf7913942acb3ac1d7cd915189b937\transformed\play-services-tasks-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-tasks:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\dacf7913942acb3ac1d7cd915189b937\transformed\play-services-tasks-16.0.1\AndroidManifest.xml:7:5-20
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9752deab1625ddc83634fcd528444f\transformed\tensorflow-lite-api-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9752deab1625ddc83634fcd528444f\transformed\tensorflow-lite-api-2.14.0\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-basement:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\afd914afebd159dc02db3b680f36ef47\transformed\play-services-basement-16.0.1\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-basement:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\afd914afebd159dc02db3b680f36ef47\transformed\play-services-basement-16.0.1\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\059dd6d75ff728d64220d44f6ce9e737\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\059dd6d75ff728d64220d44f6ce9e737\transformed\image-1.0.0-beta1\AndroidManifest.xml:7:5-20
	android:extractNativeLibs
		INJECTED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\e44b6092d301126dd6e126ccf393f4dd\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:51:9-35
	android:label
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:49:9-41
	android:fullBackupContent
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:47:9-54
	android:roundIcon
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:50:9-54
	tools:targetApi
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:53:9-29
	android:icon
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:48:9-43
	android:allowBackup
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:45:9-35
	android:theme
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:52:9-43
	android:dataExtractionRules
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:46:9-65
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:44:9-41
activity#com.arina.ai.MainActivity
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:55:9-65:20
	android:launchMode
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:59:13-44
	android:windowSoftInputMode
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:60:13-55
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:57:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:58:13-47
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:56:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:61:13-64:29
action#android.intent.action.MAIN
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:62:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:62:25-66
category#android.intent.category.LAUNCHER
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:63:17-77
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:63:27-74
activity#com.arina.ai.debug.DebugActivity
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:67:9-80:20
	android:label
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:71:13-40
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:69:13-36
	android:theme
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:70:13-47
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:68:13-48
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:debug+data:scheme:arina
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:72:13-79:29
action#android.intent.action.VIEW
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:73:17-69
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:73:25-66
category#android.intent.category.DEFAULT
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:74:17-76
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:74:27-73
category#android.intent.category.BROWSABLE
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:75:17-78
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:75:27-75
data
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:76:17-78:46
	android:host
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:77:21-41
	android:scheme
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:78:21-43
receiver#com.arina.ai.receiver.ArinaDeviceAdminReceiver
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:83:9-94:20
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:86:13-36
	android:permission
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:87:13-70
	android:description
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:85:13-67
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:84:13-62
meta-data#android.app.device_admin
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:88:13-90:56
	android:resource
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:90:17-53
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:89:17-56
intent-filter#action:name:android.app.action.DEVICE_ADMIN_ENABLED
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:91:13-93:29
action#android.app.action.DEVICE_ADMIN_ENABLED
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:92:17-82
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:92:25-79
service#com.arina.ai.service.ArinaNotificationService
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:97:9-105:19
	android:label
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:99:13-45
	android:exported
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:101:13-37
	android:permission
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:100:13-87
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:98:13-61
intent-filter#action:name:android.service.notification.NotificationListenerService
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:102:13-104:29
action#android.service.notification.NotificationListenerService
ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:103:17-99
	android:name
		ADDED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml:103:25-96
uses-sdk
INJECTED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml
INJECTED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9515e7d883f7071e783a52c22d69d92c\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation-compose:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\9515e7d883f7071e783a52c22d69d92c\transformed\hilt-navigation-compose-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie-compose:6.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f5ff6c81dc49d2b14a73be2de8c3471\transformed\lottie-compose-6.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie-compose:6.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f5ff6c81dc49d2b14a73be2de8c3471\transformed\lottie-compose-6.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\caaa22807b29cbcd058e6ca17cad94f2\transformed\material3-1.1.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\caaa22807b29cbcd058e6ca17cad94f2\transformed\material3-1.1.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\14e7cc55df6c4f938c4ba7de31cc1a0d\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.hilt:hilt-navigation:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\14e7cc55df6c4f938c4ba7de31cc1a0d\transformed\hilt-navigation-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\transforms-3\10c3370dba421968a476cecec6b43d26\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:hilt-android:2.48] C:\Users\<USER>\.gradle\caches\transforms-3\10c3370dba421968a476cecec6b43d26\transformed\hilt-android-2.48\AndroidManifest.xml:18:3-42
MERGED from [androidx.navigation:navigation-compose:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\091b89392eebd1983c97270a3ea2e88f\transformed\navigation-compose-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-compose:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\091b89392eebd1983c97270a3ea2e88f\transformed\navigation-compose-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\fac6776b119677f70c2737cb6fc008bf\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\fac6776b119677f70c2737cb6fc008bf\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\7cf805e6d5eaee3aeed98150e2b50a46\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\7cf805e6d5eaee3aeed98150e2b50a46\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\bfb81b6df8a5cc8af0dbcbd7097efa34\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\bfb81b6df8a5cc8af0dbcbd7097efa34\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\67391349161bb7d9374d84a825ccc15e\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\67391349161bb7d9374d84a825ccc15e\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\54dba944c0e1b7098d0316d302cbb815\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\54dba944c0e1b7098d0316d302cbb815\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\5e1624fbcbcbad53e75668d2c58183a2\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\5e1624fbcbcbad53e75668d2c58183a2\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2dda48ec20754d7a266c9cf078f0d0fc\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2dda48ec20754d7a266c9cf078f0d0fc\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\066c60f300013595aebf0ac49332b168\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\066c60f300013595aebf0ac49332b168\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\5aa7c16010929e5a34dceedb38be4f70\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\5aa7c16010929e5a34dceedb38be4f70\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\fb4afee682f3a03489d458264180696a\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\fb4afee682f3a03489d458264180696a\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\622d3c1d9a9fdbac4ffcf388c3880994\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\622d3c1d9a9fdbac4ffcf388c3880994\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d4e3f78e660857ffd99f26367e80f279\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d4e3f78e660857ffd99f26367e80f279\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\c4d0d11e2ae48d711635c1b7b664ae4d\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\c4d0d11e2ae48d711635c1b7b664ae4d\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0efd23bbd0106786d0ccaeef17d4176e\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0efd23bbd0106786d0ccaeef17d4176e\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\9bcbc9f8172b6c83a87505e5f846139f\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\9bcbc9f8172b6c83a87505e5f846139f\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d9b68a39eaa82fe8f489e3031923adb9\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d9b68a39eaa82fe8f489e3031923adb9\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2705adae8bd7b86a2eeefb93e6d40613\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2705adae8bd7b86a2eeefb93e6d40613\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:20:5-44
MERGED from [com.airbnb.android:lottie:6.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\be4d7956f63fad6a2908a9518ac09c51\transformed\lottie-6.3.0\AndroidManifest.xml:5:5-44
MERGED from [com.airbnb.android:lottie:6.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\be4d7956f63fad6a2908a9518ac09c51\transformed\lottie-6.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\08b9faa45c5837e0e1fbae153d52e70d\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\08b9faa45c5837e0e1fbae153d52e70d\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\895477fa5033432c05b79dc01cd2b72e\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\895477fa5033432c05b79dc01cd2b72e\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\358e273e9a2417ed24f02c311ea2ead3\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\358e273e9a2417ed24f02c311ea2ead3\transformed\fragment-1.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\e048f096732ed3286ecb33d88bdc8bcb\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\e048f096732ed3286ecb33d88bdc8bcb\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\13da72d9b6e6e44a6adeadf5ba946485\transformed\navigation-runtime-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-runtime-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\13da72d9b6e6e44a6adeadf5ba946485\transformed\navigation-runtime-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\06a32f20c9dddf4134df46d381c57bac\transformed\navigation-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-runtime:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\06a32f20c9dddf4134df46d381c57bac\transformed\navigation-runtime-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\09f534280917575e992443df7e3e43f8\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\09f534280917575e992443df7e3e43f8\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\b0f1f905e2292d255f0c59ce6b514092\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\b0f1f905e2292d255f0c59ce6b514092\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0d089874ae0a123dfa63cd7e293a8d4e\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0d089874ae0a123dfa63cd7e293a8d4e\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\cf9863b96e0fa0e3f2e2d7e5afdcba15\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\cf9863b96e0fa0e3f2e2d7e5afdcba15\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\69c5da8aabb8aa995ca7b4b522487c89\transformed\runtime-livedata-1.5.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-livedata:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\69c5da8aabb8aa995ca7b4b522487c89\transformed\runtime-livedata-1.5.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\b11b75990ac7ff378ec7eaf71211d7ff\transformed\navigation-common-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common-ktx:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\b11b75990ac7ff378ec7eaf71211d7ff\transformed\navigation-common-ktx-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\8b0c54bce6cbc8cb8a26628ee2c9174e\transformed\navigation-common-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.navigation:navigation-common:2.5.1] C:\Users\<USER>\.gradle\caches\transforms-3\8b0c54bce6cbc8cb8a26628ee2c9174e\transformed\navigation-common-2.5.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\321a77da541c7973e1faddbea63ff37d\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\321a77da541c7973e1faddbea63ff37d\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\222459b6241d56f6f6360aa54805a23e\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\222459b6241d56f6f6360aa54805a23e\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\2b3fcb89c78062811be9a3d33f24f19e\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\2b3fcb89c78062811be9a3d33f24f19e\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\0ba9bdec507852338c756d6babae2709\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\0ba9bdec507852338c756d6babae2709\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b50e821ea9d6b95ff5b074abff6c26d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b50e821ea9d6b95ff5b074abff6c26d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd145e5bd99a4f9bf8ccb9784bd6518c\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\fd145e5bd99a4f9bf8ccb9784bd6518c\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2b730e4c27221a8631d292c0c9ce1ba5\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\2b730e4c27221a8631d292c0c9ce1ba5\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b1146cfbfb3862b0a164f1a02d85268\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9b1146cfbfb3862b0a164f1a02d85268\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\91c9d373dcb2d3e3f2672d9251a8af38\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\91c9d373dcb2d3e3f2672d9251a8af38\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa8b3d3e2faa2b54bbfdbc227ec2daf1\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\aa8b3d3e2faa2b54bbfdbc227ec2daf1\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8156e413c2861308ca8853e0b0288314\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8156e413c2861308ca8853e0b0288314\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\32e62848943abee22af881a758f90e83\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\32e62848943abee22af881a758f90e83\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b15d10bbf71c8d5b5d51f157942f826\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\5b15d10bbf71c8d5b5d51f157942f826\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\af93152fa8e6106f43095cf3a3c137bd\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\af93152fa8e6106f43095cf3a3c137bd\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9ed35dba98892a7f7ec3250fc97efe05\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9ed35dba98892a7f7ec3250fc97efe05\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0daabc13164700d8e959c998861fc536\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\0daabc13164700d8e959c998861fc536\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4ca3d52b3ba6b42ff17d04a85df62a61\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\4ca3d52b3ba6b42ff17d04a85df62a61\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\18fdd20392207bb6cdbde35eebdbeb5f\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\18fdd20392207bb6cdbde35eebdbeb5f\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\031433624e52174c2636c2b5a0147ae4\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\031433624e52174c2636c2b5a0147ae4\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\237d6bc09fe36c575f671a01372b3988\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\237d6bc09fe36c575f671a01372b3988\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\e44b6092d301126dd6e126ccf393f4dd\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\e44b6092d301126dd6e126ccf393f4dd\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c659bea7b1d60839c0336fc95ad688cd\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\c659bea7b1d60839c0336fc95ad688cd\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a90e53643f840bb6fa861430ffcfb459\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a90e53643f840bb6fa861430ffcfb459\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\b5020a7b046d2f049b44c14ea4b7abda\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\b5020a7b046d2f049b44c14ea4b7abda\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f445b7eee75118b9dc957b2fbd2da47\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f445b7eee75118b9dc957b2fbd2da47\transformed\room-runtime-2.6.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6429a5d04179c1ac0746f4a3dce30db6\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\6429a5d04179c1ac0746f4a3dce30db6\transformed\room-ktx-2.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\transforms-3\4ec6c1c6460e6f2d85fc2491f8f1a9bc\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [androidx.security:security-crypto:1.1.0-alpha06] C:\Users\<USER>\.gradle\caches\transforms-3\4ec6c1c6460e6f2d85fc2491f8f1a9bc\transformed\security-crypto-1.1.0-alpha06\AndroidManifest.xml:20:5-44
MERGED from [org.tensorflow:tensorflow-lite-support:0.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\09ef185c16eb09189ca62c9e87639751\transformed\tensorflow-lite-support-0.4.4\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite-support:0.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\09ef185c16eb09189ca62c9e87639751\transformed\tensorflow-lite-support-0.4.4\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\525d51ef284cbc1414fa013ef97634c8\transformed\tensorflow-lite-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\525d51ef284cbc1414fa013ef97634c8\transformed\tensorflow-lite-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\040ec24f41c6e30600db53f228cce65b\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\040ec24f41c6e30600db53f228cce65b\transformed\sqlite-framework-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab27d6ee8f62fbac2f082d30755d9b99\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\ab27d6ee8f62fbac2f082d30755d9b99\transformed\sqlite-2.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\9aec26117782ca9d587d428657d5104a\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\9aec26117782ca9d587d428657d5104a\transformed\annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dccbf93a9877e5958cc2f239b9152d1d\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dccbf93a9877e5958cc2f239b9152d1d\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5113d6d5b69d4e02f7b7b08f78736cf\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\b5113d6d5b69d4e02f7b7b08f78736cf\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\feff2dda9233c9b99fafd9316c62231a\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\feff2dda9233c9b99fafd9316c62231a\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8d0f68a3cbcc70041c66c54f5b861c50\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8d0f68a3cbcc70041c66c54f5b861c50\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e46feee4f62b62bf5961af7b461af3b\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e46feee4f62b62bf5961af7b461af3b\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\54ad1084872b6c88bcd746dbacdd625c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\54ad1084872b6c88bcd746dbacdd625c\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\transforms-3\8cce6ecce879efb1f440331208bfe511\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.dagger:dagger-lint-aar:2.48] C:\Users\<USER>\.gradle\caches\transforms-3\8cce6ecce879efb1f440331208bfe511\transformed\dagger-lint-aar-2.48\AndroidManifest.xml:18:3-42
MERGED from [com.google.android.gms:play-services-tasks:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\dacf7913942acb3ac1d7cd915189b937\transformed\play-services-tasks-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-tasks:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\dacf7913942acb3ac1d7cd915189b937\transformed\play-services-tasks-16.0.1\AndroidManifest.xml:5:5-44
MERGED from [org.tensorflow:tensorflow-lite-support-api:0.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\0aa40285f7452496e41138935403b60a\transformed\tensorflow-lite-support-api-0.4.4\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite-support-api:0.4.4] C:\Users\<USER>\.gradle\caches\transforms-3\0aa40285f7452496e41138935403b60a\transformed\tensorflow-lite-support-api-0.4.4\AndroidManifest.xml:4:3-71
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9752deab1625ddc83634fcd528444f\transformed\tensorflow-lite-api-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [org.tensorflow:tensorflow-lite-api:2.14.0] C:\Users\<USER>\.gradle\caches\transforms-3\2e9752deab1625ddc83634fcd528444f\transformed\tensorflow-lite-api-2.14.0\AndroidManifest.xml:6:5-7:38
MERGED from [com.google.android.gms:play-services-basement:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\afd914afebd159dc02db3b680f36ef47\transformed\play-services-basement-16.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\afd914afebd159dc02db3b680f36ef47\transformed\play-services-basement-16.0.1\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\059dd6d75ff728d64220d44f6ce9e737\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.odml:image:1.0.0-beta1] C:\Users\<USER>\.gradle\caches\transforms-3\059dd6d75ff728d64220d44f6ce9e737\transformed\image-1.0.0-beta1\AndroidManifest.xml:5:5-44
	android:targetSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from C:\Users\<USER>\Desktop\Arina\app\src\main\AndroidManifest.xml
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0efd23bbd0106786d0ccaeef17d4176e\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0efd23bbd0106786d0ccaeef17d4176e\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0efd23bbd0106786d0ccaeef17d4176e\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2705adae8bd7b86a2eeefb93e6d40613\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2705adae8bd7b86a2eeefb93e6d40613\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\2705adae8bd7b86a2eeefb93e6d40613\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\0ba9bdec507852338c756d6babae2709\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b50e821ea9d6b95ff5b074abff6c26d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b50e821ea9d6b95ff5b074abff6c26d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8d0f68a3cbcc70041c66c54f5b861c50\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\8d0f68a3cbcc70041c66c54f5b861c50\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\0ba9bdec507852338c756d6babae2709\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\0ba9bdec507852338c756d6babae2709\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\0ba9bdec507852338c756d6babae2709\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\0ba9bdec507852338c756d6babae2709\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\0ba9bdec507852338c756d6babae2709\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\0ba9bdec507852338c756d6babae2709\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\0ba9bdec507852338c756d6babae2709\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b50e821ea9d6b95ff5b074abff6c26d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b50e821ea9d6b95ff5b074abff6c26d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\3b50e821ea9d6b95ff5b074abff6c26d\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\e44b6092d301126dd6e126ccf393f4dd\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\e44b6092d301126dd6e126ccf393f4dd\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\e44b6092d301126dd6e126ccf393f4dd\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.arina.ai.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\e44b6092d301126dd6e126ccf393f4dd\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\e44b6092d301126dd6e126ccf393f4dd\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\e44b6092d301126dd6e126ccf393f4dd\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\e44b6092d301126dd6e126ccf393f4dd\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\e44b6092d301126dd6e126ccf393f4dd\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.arina.ai.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\e44b6092d301126dd6e126ccf393f4dd\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\e44b6092d301126dd6e126ccf393f4dd\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f445b7eee75118b9dc957b2fbd2da47\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f445b7eee75118b9dc957b2fbd2da47\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f445b7eee75118b9dc957b2fbd2da47\transformed\room-runtime-2.6.1\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f445b7eee75118b9dc957b2fbd2da47\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\2f445b7eee75118b9dc957b2fbd2da47\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d762fefcd38d9abf538efb1c285f6cc\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\afd914afebd159dc02db3b680f36ef47\transformed\play-services-basement-16.0.1\AndroidManifest.xml:23:9-25:69
	android:value
		ADDED from [com.google.android.gms:play-services-basement:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\afd914afebd159dc02db3b680f36ef47\transformed\play-services-basement-16.0.1\AndroidManifest.xml:25:13-66
	android:name
		ADDED from [com.google.android.gms:play-services-basement:16.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\afd914afebd159dc02db3b680f36ef47\transformed\play-services-basement-16.0.1\AndroidManifest.xml:24:13-58
