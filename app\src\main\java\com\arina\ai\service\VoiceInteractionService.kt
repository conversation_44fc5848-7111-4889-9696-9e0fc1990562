package com.arina.ai.service

import android.content.Intent
import android.os.Bundle
import android.service.voice.VoiceInteractionService
import android.service.voice.VoiceInteractionSession
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class ArinaVoiceInteractionService : VoiceInteractionService() {
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)
    private val _voiceState = MutableStateFlow<VoiceState>(VoiceState.Idle)
    val voiceState: StateFlow<VoiceState> = _voiceState

    @Inject
    lateinit var wakeWordDetector: WakeWordDetector

    @Inject
    lateinit var speechRecognizer: SpeechRecognizer

    override fun onCreateSession(bundle: Bundle?): VoiceInteractionSession {
        return ArinaVoiceSession(this)
    }

    override fun onCreate() {
        super.onCreate()
        startWakeWordDetection()
    }

    private fun startWakeWordDetection() {
        serviceScope.launch {
            wakeWordDetector.startDetection { isWakeWordDetected ->
                if (isWakeWordDetected) {
                    _voiceState.value = VoiceState.WakeWordDetected
                    startListening()
                }
            }
        }
    }

    private fun startListening() {
        serviceScope.launch {
            _voiceState.value = VoiceState.Listening
            speechRecognizer.startListening { text ->
                _voiceState.value = VoiceState.Processing
                processCommand(text)
            }
        }
    }

    private fun processCommand(text: String) {
        serviceScope.launch {
            try {
                // Process command using A4F API
                _voiceState.value = VoiceState.Responding
                // Handle response
            } catch (e: Exception) {
                _voiceState.value = VoiceState.Error(e.message ?: "Unknown error")
            } finally {
                _voiceState.value = VoiceState.Idle
            }
        }
    }

    override fun onDestroy() {
        wakeWordDetector.stopDetection()
        speechRecognizer.stopListening()
        super.onDestroy()
    }
}

sealed class VoiceState {
    object Idle : VoiceState()
    object WakeWordDetected : VoiceState()
    object Listening : VoiceState()
    object Processing : VoiceState()
    object Responding : VoiceState()
    data class Error(val message: String) : VoiceState()
} 