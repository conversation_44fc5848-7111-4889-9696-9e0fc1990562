package com.arina.ai.service

import android.content.Context
import dagger.hilt.android.qualifiers.ApplicationContext
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

class VoiceInteractionService(
    private val context: Context
) {
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)
    private val _voiceState = MutableStateFlow<VoiceState>(VoiceState.Idle)
    val voiceState: StateFlow<VoiceState> = _voiceState

    // Voice result callback for MainActivity
    private var voiceResultCallback: ((String) -> Unit)? = null

    // Initialize services directly since we're not using DI here
    private val wakeWordDetector: WakeWordDetector by lazy { WakeWordDetector(context) }
    private val speechRecognizer: SpeechRecognizer by lazy { SpeechRecognizer(context) }

    fun setVoiceResultCallback(callback: (String) -> Unit) {
        voiceResultCallback = callback
    }

    fun startWakeWordDetection() {
        serviceScope.launch {
            wakeWordDetector.startDetection { isWakeWordDetected ->
                if (isWakeWordDetected) {
                    _voiceState.value = VoiceState.WakeWordDetected
                    startListening()
                }
            }
        }
    }

    private fun startListening() {
        serviceScope.launch {
            _voiceState.value = VoiceState.Listening
            speechRecognizer.startListening { text ->
                _voiceState.value = VoiceState.Processing
                processCommand(text)
            }
        }
    }

    private fun processCommand(text: String) {
        serviceScope.launch {
            try {
                _voiceState.value = VoiceState.Responding
                // Send result to MainActivity via callback
                voiceResultCallback?.invoke(text)
            } catch (e: Exception) {
                _voiceState.value = VoiceState.Error(e.message ?: "Unknown error")
                // Try to restart STT on error
                speechRecognizer.forceRestart()
            } finally {
                _voiceState.value = VoiceState.Idle
            }
        }
    }

    fun startManualListening() {
        startListening()
    }

    fun stopListening() {
        speechRecognizer.stopListening()
        _voiceState.value = VoiceState.Idle
    }

    fun release() {
        wakeWordDetector.stopDetection()
        speechRecognizer.release()
        voiceResultCallback = null
    }
}

sealed class VoiceState {
    object Idle : VoiceState()
    object WakeWordDetected : VoiceState()
    object Listening : VoiceState()
    object Processing : VoiceState()
    object Responding : VoiceState()
    data class Error(val message: String) : VoiceState()
}