package com.arina.ai.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Bookmark
import androidx.compose.material.icons.filled.BookmarkBorder
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import com.arina.ai.data.database.Conversation
import com.arina.ai.ui.theme.ArinaColors
import java.text.SimpleDateFormat
import java.util.*

@Composable
fun ConversationList(
    conversations: List<Conversation>,
    onBookmark: (Conversation) -> Unit,
    onDelete: (Conversation) -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier,
        verticalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        items(conversations) { conversation ->
            ConversationItem(
                conversation = conversation,
                onBookmark = onBookmark,
                onDelete = onDelete
            )
        }
    }
}

@Composable
private fun ConversationItem(
    conversation: Conversation,
    onBookmark: (Conversation) -> Unit,
    onDelete: (Conversation) -> Unit,
    modifier: Modifier = Modifier
) {
    HolographicCard(
        modifier = modifier.fillMaxWidth()
    ) {
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp)
        ) {
            // Header with timestamp and actions
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                Text(
                    text = SimpleDateFormat("MMM dd, HH:mm", Locale.getDefault())
                        .format(Date(conversation.timestamp)),
                    style = MaterialTheme.typography.labelMedium,
                    color = ArinaColors.TextSecondary
                )
                
                Row(
                    horizontalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    IconButton(onClick = { onBookmark(conversation) }) {
                        Icon(
                            imageVector = if (conversation.isBookmarked) {
                                Icons.Default.Bookmark
                            } else {
                                Icons.Default.BookmarkBorder
                            },
                            contentDescription = if (conversation.isBookmarked) {
                                "Remove Bookmark"
                            } else {
                                "Add Bookmark"
                            },
                            tint = if (conversation.isBookmarked) {
                                ArinaColors.NeonYellow
                            } else {
                                ArinaColors.TextSecondary
                            }
                        )
                    }
                    
                    IconButton(onClick = { onDelete(conversation) }) {
                        Icon(
                            imageVector = Icons.Default.Delete,
                            contentDescription = "Delete",
                            tint = ArinaColors.TextSecondary
                        )
                    }
                }
            }

            Spacer(modifier = Modifier.height(8.dp))

            // User Message
            Text(
                text = "You",
                style = MaterialTheme.typography.labelMedium,
                color = ArinaColors.NeonMagenta
            )
            
            Text(
                text = conversation.userMessage,
                style = MaterialTheme.typography.bodyLarge,
                color = ArinaColors.TextPrimary,
                maxLines = 3,
                overflow = TextOverflow.Ellipsis
            )

            Spacer(modifier = Modifier.height(8.dp))
            Divider(color = ArinaColors.TextSecondary.copy(alpha = 0.2f))
            Spacer(modifier = Modifier.height(8.dp))

            // ARINA Response
            Text(
                text = "ARINA",
                style = MaterialTheme.typography.labelMedium,
                color = ArinaColors.NeonCyan
            )
            
            Text(
                text = conversation.arinaResponse,
                style = MaterialTheme.typography.bodyLarge,
                color = ArinaColors.TextPrimary,
                maxLines = 5,
                overflow = TextOverflow.Ellipsis
            )

            // Emotional State Chip
            conversation.emotionalState?.let { emotion ->
                Spacer(modifier = Modifier.height(8.dp))
                SuggestionChip(
                    onClick = { },
                    label = { Text(emotion) },
                    colors = SuggestionChipDefaults.suggestionChipColors(
                        containerColor = ArinaColors.NeonMagenta.copy(alpha = 0.2f),
                        labelColor = ArinaColors.NeonMagenta
                    )
                )
            }
        }
    }
} 