package com.arina.ai.ui.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.gestures.detectTapGestures
import androidx.compose.foundation.layout.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.draw.drawBehind
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.graphicsLayer
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onGloballyPositioned
import androidx.compose.ui.unit.dp
import com.arina.ai.ui.theme.ArinaColors
import com.arina.ai.ui.theme.CyberCard
import kotlin.math.PI
import kotlin.math.cos
import kotlin.math.sin

@Composable
fun HolographicCard(
    modifier: Modifier = Modifier,
    content: @Composable () -> Unit
) {
    var cardSize by remember { mutableStateOf(Offset.Zero) }
    var touchPosition by remember { mutableStateOf(Offset.Zero) }
    var isTouched by remember { mutableStateOf(false) }

    val infiniteTransition = rememberInfiniteTransition()
    
    // Holographic animation
    val shimmerPhase by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 2f * PI.toFloat(),
        animationSpec = infiniteRepeatable(
            animation = tween(3000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        )
    )

    // Border animation
    val borderPhase by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        )
    )

    // 3D tilt animation based on touch position
    val rotationX = if (isTouched && cardSize.y != 0f) {
        (touchPosition.y - cardSize.y / 2) / (cardSize.y / 2) * 10f
    } else {
        0f
    }

    val rotationY = if (isTouched && cardSize.x != 0f) {
        -(touchPosition.x - cardSize.x / 2) / (cardSize.x / 2) * 10f
    } else {
        0f
    }

    Box(
        modifier = modifier
            .clip(CyberCard)
            .onGloballyPositioned { coordinates ->
                cardSize = Offset(
                    coordinates.size.width.toFloat(),
                    coordinates.size.height.toFloat()
                )
            }
            .pointerInput(Unit) {
                detectTapGestures(
                    onPress = { offset ->
                        touchPosition = offset
                        isTouched = true
                        tryAwaitRelease()
                        isTouched = false
                    }
                )
            }
            .graphicsLayer {
                rotationX = rotationX
                rotationY = rotationY
                cameraDistance = 12f * density
            }
            .shadow(
                elevation = 8.dp,
                shape = CyberCard,
                spotColor = ArinaColors.NeonCyan.copy(alpha = 0.5f)
            )
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(
                        ArinaColors.SurfacePrimary,
                        ArinaColors.SurfaceSecondary
                    )
                )
            )
            .drawBehind {
                // Draw holographic effect
                val shimmerWidth = size.width * 0.2f
                val shimmerX = (size.width + shimmerWidth) * 
                    (sin(shimmerPhase) * 0.5f + 0.5f) - shimmerWidth

                drawRect(
                    brush = Brush.horizontalGradient(
                        colors = listOf(
                            Color.Transparent,
                            ArinaColors.NeonCyan.copy(alpha = 0.2f),
                            ArinaColors.NeonMagenta.copy(alpha = 0.2f),
                            Color.Transparent
                        ),
                        startX = shimmerX,
                        endX = shimmerX + shimmerWidth
                    )
                )

                // Draw animated corner accents
                val cornerSize = size.width * 0.1f
                repeat(4) { corner ->
                    val rotation = borderPhase + corner * 90f
                    val x = when (corner) {
                        0, 3 -> 0f
                        else -> size.width
                    }
                    val y = when (corner) {
                        0, 1 -> 0f
                        else -> size.height
                    }
                    
                    drawLine(
                        color = ArinaColors.NeonCyan,
                        start = Offset(x, y),
                        end = Offset(
                            x + if (corner % 2 == 0) cornerSize else -cornerSize,
                            y + if (corner < 2) cornerSize else -cornerSize
                        ),
                        strokeWidth = 2.dp.toPx(),
                        alpha = 0.8f
                    )
                }
            }
            .border(
                width = 1.dp,
                brush = Brush.linearGradient(
                    colors = listOf(
                        ArinaColors.NeonCyan.copy(alpha = 0.5f),
                        ArinaColors.NeonMagenta.copy(alpha = 0.5f),
                        ArinaColors.NeonCyan.copy(alpha = 0.5f)
                    )
                ),
                shape = CyberCard
            )
            .padding(16.dp)
    ) {
        content()
    }
} 