package com.arina.ai.service

import android.app.Notification
import android.service.notification.NotificationListenerService
import android.service.notification.StatusBarNotification
import android.speech.tts.TextToSpeech
import com.arina.ai.data.database.ArinaDatabase
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import java.util.*

class ArinaNotificationService : NotificationListenerService() {
    private lateinit var textToSpeech: TextToSpeech
    private lateinit var database: ArinaDatabase
    private val serviceScope = CoroutineScope(Dispatchers.IO)

    override fun onCreate() {
        super.onCreate()
        database = ArinaDatabase.getInstance(applicationContext)
        initializeTextToSpeech()
    }

    private fun initializeTextToSpeech() {
        textToSpeech = TextToSpeech(this) { status ->
            if (status == TextToSpeech.SUCCESS) {
                textToSpeech.language = Locale.US
            }
        }
    }

    override fun onNotificationPosted(sbn: StatusBarNotification) {
        serviceScope.launch {
            val preferences = database.userPreferencesDao().getUserPreferences()
            if (preferences?.notificationSpeakerEnabled == true) {
                val notification = sbn.notification
                val title = notification.extras.getString(Notification.EXTRA_TITLE) ?: ""
                val text = notification.extras.getString(Notification.EXTRA_TEXT) ?: ""
                
                // Speak notification if it's important
                if (notification.flags and Notification.FLAG_HIGH_PRIORITY != 0) {
                    speakNotification(title, text)
                }

                // Store notification in database for context
                storeNotification(sbn)
            }
        }
    }

    private fun speakNotification(title: String, text: String) {
        val speechText = "New notification from $title: $text"
        textToSpeech.speak(
            speechText,
            TextToSpeech.QUEUE_ADD,
            null,
            UUID.randomUUID().toString()
        )
    }

    private suspend fun storeNotification(sbn: StatusBarNotification) {
        val notification = sbn.notification
        val title = notification.extras.getString(Notification.EXTRA_TITLE) ?: ""
        val text = notification.extras.getString(Notification.EXTRA_TEXT) ?: ""
        
        // Store in conversation history
        database.conversationDao().insertConversation(
            com.arina.ai.data.database.Conversation(
                userMessage = "Notification: $title",
                arinaResponse = text,
                emotionalState = "HELPFUL"
            )
        )
    }

    override fun onNotificationRemoved(sbn: StatusBarNotification) {
        // Handle notification removal if needed
    }

    override fun onDestroy() {
        super.onDestroy()
        textToSpeech.shutdown()
    }
}