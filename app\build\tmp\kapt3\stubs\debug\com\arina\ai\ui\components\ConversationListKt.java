package com.arina.ai.ui.components;

@kotlin.Metadata(mv = {1, 9, 0}, k = 2, xi = 48, d1 = {"\u0000$\n\u0000\n\u0002\u0010\u0002\n\u0000\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0010 \n\u0000\u001aB\u0010\u0000\u001a\u00020\u00012\u0006\u0010\u0002\u001a\u00020\u00032\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0003\u001aH\u0010\t\u001a\u00020\u00012\f\u0010\n\u001a\b\u0012\u0004\u0012\u00020\u00030\u000b2\u0012\u0010\u0004\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\u0012\u0010\u0006\u001a\u000e\u0012\u0004\u0012\u00020\u0003\u0012\u0004\u0012\u00020\u00010\u00052\b\b\u0002\u0010\u0007\u001a\u00020\bH\u0007\u00a8\u0006\f"}, d2 = {"ConversationItem", "", "conversation", "Lcom/arina/ai/data/database/Conversation;", "onBookmark", "Lkotlin/Function1;", "onDelete", "modifier", "Landroidx/compose/ui/Modifier;", "ConversationList", "conversations", "", "app_debug"})
public final class ConversationListKt {
    
    @androidx.compose.runtime.Composable
    public static final void ConversationList(@org.jetbrains.annotations.NotNull
    java.util.List<com.arina.ai.data.database.Conversation> conversations, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.arina.ai.data.database.Conversation, kotlin.Unit> onBookmark, @org.jetbrains.annotations.NotNull
    kotlin.jvm.functions.Function1<? super com.arina.ai.data.database.Conversation, kotlin.Unit> onDelete, @org.jetbrains.annotations.NotNull
    androidx.compose.ui.Modifier modifier) {
    }
    
    @androidx.compose.runtime.Composable
    private static final void ConversationItem(com.arina.ai.data.database.Conversation conversation, kotlin.jvm.functions.Function1<? super com.arina.ai.data.database.Conversation, kotlin.Unit> onBookmark, kotlin.jvm.functions.Function1<? super com.arina.ai.data.database.Conversation, kotlin.Unit> onDelete, androidx.compose.ui.Modifier modifier) {
    }
}