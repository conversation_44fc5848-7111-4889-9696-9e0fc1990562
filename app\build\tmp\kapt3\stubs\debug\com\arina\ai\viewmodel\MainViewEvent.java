package com.arina.ai.viewmodel;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u00002\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\n\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\b6\u0018\u00002\u00020\u0001:\t\u0003\u0004\u0005\u0006\u0007\b\t\n\u000bB\u0007\b\u0004\u00a2\u0006\u0002\u0010\u0002\u0082\u0001\t\f\r\u000e\u000f\u0010\u0011\u0012\u0013\u0014\u00a8\u0006\u0015"}, d2 = {"Lcom/arina/ai/viewmodel/MainViewEvent;", "", "()V", "BookmarkConversation", "ClearError", "DeleteConversation", "ExecuteSystemCommand", "ProcessTextInput", "ProcessVoiceInput", "StartListening", "StopListening", "UpdatePreferences", "Lcom/arina/ai/viewmodel/MainViewEvent$BookmarkConversation;", "Lcom/arina/ai/viewmodel/MainViewEvent$ClearError;", "Lcom/arina/ai/viewmodel/MainViewEvent$DeleteConversation;", "Lcom/arina/ai/viewmodel/MainViewEvent$ExecuteSystemCommand;", "Lcom/arina/ai/viewmodel/MainViewEvent$ProcessTextInput;", "Lcom/arina/ai/viewmodel/MainViewEvent$ProcessVoiceInput;", "Lcom/arina/ai/viewmodel/MainViewEvent$StartListening;", "Lcom/arina/ai/viewmodel/MainViewEvent$StopListening;", "Lcom/arina/ai/viewmodel/MainViewEvent$UpdatePreferences;", "app_debug"})
public abstract class MainViewEvent {
    
    private MainViewEvent() {
        super();
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0011"}, d2 = {"Lcom/arina/ai/viewmodel/MainViewEvent$BookmarkConversation;", "Lcom/arina/ai/viewmodel/MainViewEvent;", "conversation", "Lcom/arina/ai/data/database/Conversation;", "(Lcom/arina/ai/data/database/Conversation;)V", "getConversation", "()Lcom/arina/ai/data/database/Conversation;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
    public static final class BookmarkConversation extends com.arina.ai.viewmodel.MainViewEvent {
        @org.jetbrains.annotations.NotNull
        private final com.arina.ai.data.database.Conversation conversation = null;
        
        public BookmarkConversation(@org.jetbrains.annotations.NotNull
        com.arina.ai.data.database.Conversation conversation) {
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.arina.ai.data.database.Conversation getConversation() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.arina.ai.data.database.Conversation component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.arina.ai.viewmodel.MainViewEvent.BookmarkConversation copy(@org.jetbrains.annotations.NotNull
        com.arina.ai.data.database.Conversation conversation) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/arina/ai/viewmodel/MainViewEvent$ClearError;", "Lcom/arina/ai/viewmodel/MainViewEvent;", "()V", "app_debug"})
    public static final class ClearError extends com.arina.ai.viewmodel.MainViewEvent {
        @org.jetbrains.annotations.NotNull
        public static final com.arina.ai.viewmodel.MainViewEvent.ClearError INSTANCE = null;
        
        private ClearError() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0011"}, d2 = {"Lcom/arina/ai/viewmodel/MainViewEvent$DeleteConversation;", "Lcom/arina/ai/viewmodel/MainViewEvent;", "conversation", "Lcom/arina/ai/data/database/Conversation;", "(Lcom/arina/ai/data/database/Conversation;)V", "getConversation", "()Lcom/arina/ai/data/database/Conversation;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
    public static final class DeleteConversation extends com.arina.ai.viewmodel.MainViewEvent {
        @org.jetbrains.annotations.NotNull
        private final com.arina.ai.data.database.Conversation conversation = null;
        
        public DeleteConversation(@org.jetbrains.annotations.NotNull
        com.arina.ai.data.database.Conversation conversation) {
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.arina.ai.data.database.Conversation getConversation() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.arina.ai.data.database.Conversation component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.arina.ai.viewmodel.MainViewEvent.DeleteConversation copy(@org.jetbrains.annotations.NotNull
        com.arina.ai.data.database.Conversation conversation) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0011"}, d2 = {"Lcom/arina/ai/viewmodel/MainViewEvent$ExecuteSystemCommand;", "Lcom/arina/ai/viewmodel/MainViewEvent;", "command", "Lcom/arina/ai/viewmodel/SystemCommand;", "(Lcom/arina/ai/viewmodel/SystemCommand;)V", "getCommand", "()Lcom/arina/ai/viewmodel/SystemCommand;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
    public static final class ExecuteSystemCommand extends com.arina.ai.viewmodel.MainViewEvent {
        @org.jetbrains.annotations.NotNull
        private final com.arina.ai.viewmodel.SystemCommand command = null;
        
        public ExecuteSystemCommand(@org.jetbrains.annotations.NotNull
        com.arina.ai.viewmodel.SystemCommand command) {
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.arina.ai.viewmodel.SystemCommand getCommand() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.arina.ai.viewmodel.SystemCommand component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.arina.ai.viewmodel.MainViewEvent.ExecuteSystemCommand copy(@org.jetbrains.annotations.NotNull
        com.arina.ai.viewmodel.SystemCommand command) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/arina/ai/viewmodel/MainViewEvent$ProcessTextInput;", "Lcom/arina/ai/viewmodel/MainViewEvent;", "text", "", "(Ljava/lang/String;)V", "getText", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_debug"})
    public static final class ProcessTextInput extends com.arina.ai.viewmodel.MainViewEvent {
        @org.jetbrains.annotations.NotNull
        private final java.lang.String text = null;
        
        public ProcessTextInput(@org.jetbrains.annotations.NotNull
        java.lang.String text) {
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getText() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.arina.ai.viewmodel.MainViewEvent.ProcessTextInput copy(@org.jetbrains.annotations.NotNull
        java.lang.String text) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000&\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0002\b\u0002\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0003H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/arina/ai/viewmodel/MainViewEvent$ProcessVoiceInput;", "Lcom/arina/ai/viewmodel/MainViewEvent;", "text", "", "(Ljava/lang/String;)V", "getText", "()Ljava/lang/String;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "app_debug"})
    public static final class ProcessVoiceInput extends com.arina.ai.viewmodel.MainViewEvent {
        @org.jetbrains.annotations.NotNull
        private final java.lang.String text = null;
        
        public ProcessVoiceInput(@org.jetbrains.annotations.NotNull
        java.lang.String text) {
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String getText() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final java.lang.String component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.arina.ai.viewmodel.MainViewEvent.ProcessVoiceInput copy(@org.jetbrains.annotations.NotNull
        java.lang.String text) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000$\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000b\n\u0002\b\u0007\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\u000f\u0012\b\b\u0002\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\u00032\b\u0010\n\u001a\u0004\u0018\u00010\u000bH\u00d6\u0003J\t\u0010\f\u001a\u00020\rH\u00d6\u0001J\t\u0010\u000e\u001a\u00020\u000fH\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0010"}, d2 = {"Lcom/arina/ai/viewmodel/MainViewEvent$StartListening;", "Lcom/arina/ai/viewmodel/MainViewEvent;", "wakeWordDetected", "", "(Z)V", "getWakeWordDetected", "()Z", "component1", "copy", "equals", "other", "", "hashCode", "", "toString", "", "app_debug"})
    public static final class StartListening extends com.arina.ai.viewmodel.MainViewEvent {
        private final boolean wakeWordDetected = false;
        
        public StartListening(boolean wakeWordDetected) {
        }
        
        public final boolean getWakeWordDetected() {
            return false;
        }
        
        public StartListening() {
        }
        
        public final boolean component1() {
            return false;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.arina.ai.viewmodel.MainViewEvent.StartListening copy(boolean wakeWordDetected) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\f\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u00c6\u0002\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002\u00a8\u0006\u0003"}, d2 = {"Lcom/arina/ai/viewmodel/MainViewEvent$StopListening;", "Lcom/arina/ai/viewmodel/MainViewEvent;", "()V", "app_debug"})
    public static final class StopListening extends com.arina.ai.viewmodel.MainViewEvent {
        @org.jetbrains.annotations.NotNull
        public static final com.arina.ai.viewmodel.MainViewEvent.StopListening INSTANCE = null;
        
        private StopListening() {
        }
    }
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000*\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0006\n\u0002\u0010\u000b\n\u0000\n\u0002\u0010\u0000\n\u0000\n\u0002\u0010\b\n\u0000\n\u0002\u0010\u000e\n\u0000\b\u0086\b\u0018\u00002\u00020\u0001B\r\u0012\u0006\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\u0002\u0010\u0004J\t\u0010\u0007\u001a\u00020\u0003H\u00c6\u0003J\u0013\u0010\b\u001a\u00020\u00002\b\b\u0002\u0010\u0002\u001a\u00020\u0003H\u00c6\u0001J\u0013\u0010\t\u001a\u00020\n2\b\u0010\u000b\u001a\u0004\u0018\u00010\fH\u00d6\u0003J\t\u0010\r\u001a\u00020\u000eH\u00d6\u0001J\t\u0010\u000f\u001a\u00020\u0010H\u00d6\u0001R\u0011\u0010\u0002\u001a\u00020\u0003\u00a2\u0006\b\n\u0000\u001a\u0004\b\u0005\u0010\u0006\u00a8\u0006\u0011"}, d2 = {"Lcom/arina/ai/viewmodel/MainViewEvent$UpdatePreferences;", "Lcom/arina/ai/viewmodel/MainViewEvent;", "preferences", "Lcom/arina/ai/data/database/UserPreferences;", "(Lcom/arina/ai/data/database/UserPreferences;)V", "getPreferences", "()Lcom/arina/ai/data/database/UserPreferences;", "component1", "copy", "equals", "", "other", "", "hashCode", "", "toString", "", "app_debug"})
    public static final class UpdatePreferences extends com.arina.ai.viewmodel.MainViewEvent {
        @org.jetbrains.annotations.NotNull
        private final com.arina.ai.data.database.UserPreferences preferences = null;
        
        public UpdatePreferences(@org.jetbrains.annotations.NotNull
        com.arina.ai.data.database.UserPreferences preferences) {
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.arina.ai.data.database.UserPreferences getPreferences() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.arina.ai.data.database.UserPreferences component1() {
            return null;
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.arina.ai.viewmodel.MainViewEvent.UpdatePreferences copy(@org.jetbrains.annotations.NotNull
        com.arina.ai.data.database.UserPreferences preferences) {
            return null;
        }
        
        @java.lang.Override
        public boolean equals(@org.jetbrains.annotations.Nullable
        java.lang.Object other) {
            return false;
        }
        
        @java.lang.Override
        public int hashCode() {
            return 0;
        }
        
        @java.lang.Override
        @org.jetbrains.annotations.NotNull
        public java.lang.String toString() {
            return null;
        }
    }
}