package com.arina.ai.ui.components

import androidx.compose.animation.core.*
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.drawscope.rotate
import androidx.compose.ui.unit.dp
import com.arina.ai.ui.theme.ArinaColors
import kotlin.math.*

enum class EmotionalState {
    CALM,
    FOCUSED,
    EXCITED,
    CONCERNED,
    CONFUSED
}

@Composable
fun PersonalityAvatar(
    emotionalState: EmotionalState,
    modifier: Modifier = Modifier
) {
    val infiniteTransition = rememberInfiniteTransition()
    
    // Core rotation animation
    val coreRotation by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 360f,
        animationSpec = infiniteRepeatable(
            animation = tween(10000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        )
    )

    // Pulse animation
    val pulseScale by infiniteTransition.animateFloat(
        initialValue = 0.8f,
        targetValue = 1.2f,
        animationSpec = infiniteRepeatable(
            animation = tween(1000, easing = FastOutSlowInEasing),
            repeatMode = RepeatMode.Reverse
        )
    )

    // Wave animation
    val wavePhase by infiniteTransition.animateFloat(
        initialValue = 0f,
        targetValue = 2f * PI.toFloat(),
        animationSpec = infiniteRepeatable(
            animation = tween(2000, easing = LinearEasing),
            repeatMode = RepeatMode.Restart
        )
    )

    // Emotional state specific properties
    val (primaryColor, secondaryColor, intensity) = when (emotionalState) {
        EmotionalState.CALM -> Triple(ArinaColors.NeonCyan, ArinaColors.NeonBlue, 0.5f)
        EmotionalState.FOCUSED -> Triple(ArinaColors.NeonMagenta, ArinaColors.NeonCyan, 0.8f)
        EmotionalState.EXCITED -> Triple(ArinaColors.NeonYellow, ArinaColors.NeonMagenta, 1.0f)
        EmotionalState.CONCERNED -> Triple(ArinaColors.NeonOrange, ArinaColors.NeonRed, 0.7f)
        EmotionalState.CONFUSED -> Triple(ArinaColors.NeonPurple, ArinaColors.NeonRed, 0.6f)
    }

    Box(modifier = modifier.aspectRatio(1f)) {
        Canvas(modifier = Modifier.matchParentSize()) {
            val center = Offset(size.width / 2, size.height / 2)
            val radius = size.minDimension / 3

            // Draw core circle
            drawCircle(
                color = primaryColor,
                radius = radius * pulseScale,
                center = center,
                style = Stroke(
                    width = 4.dp.toPx(),
                    pathEffect = PathEffect.dashPathEffect(
                        intervals = floatArrayOf(10f, 10f),
                        phase = wavePhase
                    )
                )
            )

            // Draw rotating segments
            rotate(coreRotation, center) {
                val segmentCount = 8
                val angleStep = 360f / segmentCount
                repeat(segmentCount) { i ->
                    val angle = i * angleStep
                    val startRadius = radius * 1.2f
                    val endRadius = radius * 1.5f
                    val startPoint = Offset(
                        x = center.x + cos(Math.toRadians(angle.toDouble())).toFloat() * startRadius,
                        y = center.y + sin(Math.toRadians(angle.toDouble())).toFloat() * startRadius
                    )
                    val endPoint = Offset(
                        x = center.x + cos(Math.toRadians(angle.toDouble())).toFloat() * endRadius,
                        y = center.y + sin(Math.toRadians(angle.toDouble())).toFloat() * endRadius
                    )
                    drawLine(
                        color = secondaryColor.copy(alpha = intensity),
                        start = startPoint,
                        end = endPoint,
                        strokeWidth = 2.dp.toPx()
                    )
                }
            }

            // Draw energy waves
            repeat(3) { i ->
                val waveRadius = radius * (1.5f + i * 0.2f) * pulseScale
                drawCircle(
                    color = primaryColor.copy(alpha = (0.3f - i * 0.1f) * intensity),
                    radius = waveRadius,
                    center = center,
                    style = Stroke(
                        width = 2.dp.toPx(),
                        pathEffect = PathEffect.dashPathEffect(
                            intervals = floatArrayOf(5f, 10f),
                            phase = wavePhase + i * 30f
                        )
                    )
                )
            }

            // Draw emotional state indicators
            val indicatorCount = 12
            val indicatorAngleStep = 360f / indicatorCount
            repeat(indicatorCount) { i ->
                val angle = i * indicatorAngleStep + coreRotation
                val indicatorRadius = radius * 0.8f
                val indicatorPoint = Offset(
                    x = center.x + cos(Math.toRadians(angle.toDouble())).toFloat() * indicatorRadius,
                    y = center.y + sin(Math.toRadians(angle.toDouble())).toFloat() * indicatorRadius
                )
                drawCircle(
                    color = secondaryColor.copy(alpha = intensity),
                    radius = 3.dp.toPx(),
                    center = indicatorPoint
                )
            }
        }
    }
}