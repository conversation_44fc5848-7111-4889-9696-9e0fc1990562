package com.arina.ai.receiver;

import dagger.hilt.InstallIn;
import dagger.hilt.codegen.OriginatingElement;
import dagger.hilt.components.SingletonComponent;
import dagger.hilt.internal.GeneratedEntryPoint;
import javax.annotation.processing.Generated;

@OriginatingElement(
    topLevelClass = ArinaDeviceAdminReceiver.class
)
@GeneratedEntryPoint
@InstallIn(SingletonComponent.class)
@Generated("dagger.hilt.android.processor.internal.androidentrypoint.InjectorEntryPointGenerator")
public interface ArinaDeviceAdminReceiver_GeneratedInjector {
  void injectArinaDeviceAdminReceiver(ArinaDeviceAdminReceiver arinaDeviceAdminReceiver);
}
