package com.arina.ai.service

import android.app.Notification
import android.service.notification.NotificationListenerService
import android.service.notification.StatusBarNotification
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject

@AndroidEntryPoint
class ArinaNotificationListenerService : NotificationListenerService() {
    private val serviceScope = CoroutineScope(SupervisorJob() + Dispatchers.Default)
    private val _notifications = MutableStateFlow<List<NotificationInfo>>(emptyList())
    val notifications: StateFlow<List<NotificationInfo>> = _notifications

    override fun onNotificationPosted(sbn: StatusBarNotification) {
        serviceScope.launch {
            val notification = sbn.notification
            val extras = notification.extras
            
            val notificationInfo = NotificationInfo(
                id = sbn.id,
                packageName = sbn.packageName,
                title = extras.getString(Notification.EXTRA_TITLE) ?: "",
                text = extras.getString(Notification.EXTRA_TEXT) ?: "",
                timestamp = sbn.postTime,
                isOngoing = sbn.isOngoing,
                isClearable = sbn.isClearable,
                category = notification.category ?: "unknown"
            )

            val currentList = _notifications.value.toMutableList()
            currentList.add(0, notificationInfo)
            _notifications.value = currentList
        }
    }

    override fun onNotificationRemoved(sbn: StatusBarNotification) {
        serviceScope.launch {
            val currentList = _notifications.value.toMutableList()
            currentList.removeAll { it.id == sbn.id }
            _notifications.value = currentList
        }
    }

    fun clearNotification(key: String) {
        cancelNotification(key)
    }

    fun clearAllNotifications() {
        cancelAllNotifications()
    }
}

data class NotificationInfo(
    val id: Int,
    val packageName: String,
    val title: String,
    val text: String,
    val timestamp: Long,
    val isOngoing: Boolean,
    val isClearable: Boolean,
    val category: String
) 