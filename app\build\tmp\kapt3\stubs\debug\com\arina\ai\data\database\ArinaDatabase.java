package com.arina.ai.data.database;

@kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000\u001a\n\u0002\u0018\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\'\u0018\u0000 \u00072\u00020\u0001:\u0001\u0007B\u0005\u00a2\u0006\u0002\u0010\u0002J\b\u0010\u0003\u001a\u00020\u0004H&J\b\u0010\u0005\u001a\u00020\u0006H&\u00a8\u0006\b"}, d2 = {"Lcom/arina/ai/data/database/ArinaDatabase;", "Landroidx/room/RoomDatabase;", "()V", "conversationDao", "Lcom/arina/ai/data/database/ConversationDao;", "userPreferencesDao", "Lcom/arina/ai/data/database/UserPreferencesDao;", "Companion", "app_debug"})
@androidx.room.Database(entities = {com.arina.ai.data.database.Conversation.class, com.arina.ai.data.database.UserPreferences.class}, version = 1, exportSchema = false)
public abstract class ArinaDatabase extends androidx.room.RoomDatabase {
    @kotlin.jvm.Volatile
    @org.jetbrains.annotations.Nullable
    private static volatile com.arina.ai.data.database.ArinaDatabase INSTANCE;
    @org.jetbrains.annotations.NotNull
    public static final com.arina.ai.data.database.ArinaDatabase.Companion Companion = null;
    
    public ArinaDatabase() {
        super();
    }
    
    @org.jetbrains.annotations.NotNull
    public abstract com.arina.ai.data.database.ConversationDao conversationDao();
    
    @org.jetbrains.annotations.NotNull
    public abstract com.arina.ai.data.database.UserPreferencesDao userPreferencesDao();
    
    @kotlin.Metadata(mv = {1, 9, 0}, k = 1, xi = 48, d1 = {"\u0000(\n\u0002\u0018\u0002\n\u0002\u0010\u0000\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0002\b\u0002\n\u0002\u0018\u0002\n\u0000\n\u0002\u0010\u000e\n\u0000\n\u0002\u0018\u0002\n\u0002\b\u0002\b\u0086\u0003\u0018\u00002\u00020\u0001B\u0007\b\u0002\u00a2\u0006\u0002\u0010\u0002J\u0010\u0010\u0005\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00020\u0007H\u0002J\u0010\u0010\b\u001a\u00020\t2\u0006\u0010\n\u001a\u00020\u000bH\u0002J\u000e\u0010\f\u001a\u00020\u00042\u0006\u0010\u0006\u001a\u00020\u0007R\u0010\u0010\u0003\u001a\u0004\u0018\u00010\u0004X\u0082\u000e\u00a2\u0006\u0002\n\u0000\u00a8\u0006\r"}, d2 = {"Lcom/arina/ai/data/database/ArinaDatabase$Companion;", "", "()V", "INSTANCE", "Lcom/arina/ai/data/database/ArinaDatabase;", "createDatabase", "context", "Landroid/content/Context;", "generateAndSaveNewKey", "", "encryptedPrefs", "Landroidx/security/crypto/EncryptedSharedPreferences;", "getInstance", "app_debug"})
    public static final class Companion {
        
        private Companion() {
            super();
        }
        
        @org.jetbrains.annotations.NotNull
        public final com.arina.ai.data.database.ArinaDatabase getInstance(@org.jetbrains.annotations.NotNull
        android.content.Context context) {
            return null;
        }
        
        private final com.arina.ai.data.database.ArinaDatabase createDatabase(android.content.Context context) {
            return null;
        }
        
        private final java.lang.String generateAndSaveNewKey(androidx.security.crypto.EncryptedSharedPreferences encryptedPrefs) {
            return null;
        }
    }
}