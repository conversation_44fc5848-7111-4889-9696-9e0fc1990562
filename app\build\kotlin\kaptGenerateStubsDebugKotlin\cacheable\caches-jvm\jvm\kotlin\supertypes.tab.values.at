/ Header Record For PersistentHashMapValueStorage android.app.Application$ #androidx.activity.ComponentActivity androidx.room.RoomDatabase$ #androidx.activity.ComponentActivity& %android.app.admin.DeviceAdminReceiver9 8android.service.notification.NotificationListenerService& %com.arina.ai.service.RecognitionState& %com.arina.ai.service.RecognitionState& %com.arina.ai.service.RecognitionState& %com.arina.ai.service.RecognitionState& %com.arina.ai.service.RecognitionState& %com.arina.ai.service.RecognitionState& %com.arina.ai.service.RecognitionState& %com.arina.ai.service.RecognitionState  com.arina.ai.service.VoiceState  com.arina.ai.service.VoiceState  com.arina.ai.service.VoiceState  com.arina.ai.service.VoiceState  com.arina.ai.service.VoiceState  com.arina.ai.service.VoiceState kotlin.Enum# "androidx.compose.ui.graphics.Shape# "androidx.compose.ui.graphics.Shape# "androidx.compose.ui.graphics.Shape% $com.arina.ai.viewmodel.MainViewEvent% $com.arina.ai.viewmodel.MainViewEvent% $com.arina.ai.viewmodel.MainViewEvent% $com.arina.ai.viewmodel.MainViewEvent% $com.arina.ai.viewmodel.MainViewEvent% $com.arina.ai.viewmodel.MainViewEvent% $com.arina.ai.viewmodel.MainViewEvent% $com.arina.ai.viewmodel.MainViewEvent% $com.arina.ai.viewmodel.MainViewEvent% $com.arina.ai.viewmodel.SystemCommand% $com.arina.ai.viewmodel.SystemCommand% $com.arina.ai.viewmodel.SystemCommand% $com.arina.ai.viewmodel.SystemCommand% $com.arina.ai.viewmodel.SystemCommand% $com.arina.ai.viewmodel.SystemCommand% $com.arina.ai.viewmodel.SystemCommand% $com.arina.ai.viewmodel.SystemCommand% $com.arina.ai.viewmodel.SystemCommand% $com.arina.ai.viewmodel.SystemCommand% $com.arina.ai.viewmodel.SystemCommand% $com.arina.ai.viewmodel.SystemCommand% $com.arina.ai.viewmodel.SystemCommand androidx.lifecycle.ViewModel$ #androidx.activity.ComponentActivity